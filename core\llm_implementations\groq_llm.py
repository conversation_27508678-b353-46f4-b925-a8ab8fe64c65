from groq import Groq, APIConnectionError, AuthenticationError, RateLimitError, APIError
from ..llm_interface import LLMInterface

class GroqLLM(LLMInterface):
    """LLMInterface implementation for Groq API."""

    def __init__(self, api_key: str, model_id: str = None):
        """
        Initializes the Groq LLM client.

        Args:
            api_key: The Groq API key.
            model_id: The specific Groq model ID to use by default (optional).

        Raises:
            ValueError: If the API key is missing.
            ConnectionError: If client initialization fails.
        """
        if not api_key:
            raise ValueError("Groq API key is required.")
        super().__init__(api_key=api_key, model_id=model_id)
        try:
            # The Groq client automatically picks up the key from the
            # GROQ_API_KEY environment variable if set, but we explicitly
            # pass it here for clarity and direct control via the settings UI.
            self.client = Groq(api_key=self.api_key)
        except Exception as e:
            # Catch potential issues during client initialization
            raise ConnectionError(f"Failed to initialize Groq client: {e}") from e

    def generate(self, prompt: str, model_id: str = None, max_tokens: int = 2048, temperature: float = 0.7) -> str:
        """
        Generates text using the specified Groq model.

        Args:
            prompt: The input prompt.
            model_id: The specific Groq model ID to use (overrides default).
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.

        Returns:
            The generated text.

        Raises:
            ValueError: If model_id is not specified.
            ConnectionError: If there's an API connection or authentication issue.
            Exception: For other API errors (e.g., rate limits, invalid requests).
        """
        target_model = model_id or self.model_id
        if not target_model:
            raise ValueError("Groq Model ID must be specified either during initialization or generation.")

        try:
            chat_completion = self.client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=target_model,
                max_tokens=max_tokens,
                temperature=temperature,
                # stream=False # Ensure streaming is off
            )
            # Ensure response.choices exists and has at least one choice
            if chat_completion.choices and len(chat_completion.choices) > 0:
                 # Check if the message content is not None
                content = chat_completion.choices[0].message.content
                if content is not None:
                    return content.strip()
                else:
                    print("Warning: Groq response message content was None.")
                    return "" # Or raise Exception("Generation resulted in None content")
            else:
                raise APIError("Invalid response format from Groq: No choices found.", response=chat_completion) # Pass the raw response if possible

        except AuthenticationError as e:
            raise ConnectionError(f"Groq authentication failed: Invalid API Key? ({e})") from e
        except APIConnectionError as e:
            raise ConnectionError(f"Groq API connection error: {e}") from e
        except RateLimitError as e:
            raise Exception(f"Groq rate limit exceeded: {e}") from e
        except APIError as e: # Catch other Groq API errors
             raise Exception(f"Groq API error: Status={e.status_code}, Message={e.body.get('message', 'Unknown error') if e.body else 'Unknown error'}") from e
        except Exception as e:
            # Catch-all for unexpected errors
            raise Exception(f"An unexpected error occurred during Groq generation: {e}") from e

    def list_models(self) -> list[str]:
        """
        Fetches the list of available models from Groq.

        Returns:
            A list of model ID strings.

        Raises:
            ConnectionError: If there's an API connection or authentication issue.
            Exception: For other API errors.
        """
        try:
            models_response = self.client.models.list()
            # Ensure models_response.data exists and is iterable
            if models_response and hasattr(models_response, 'data'):
                # Filter out potential None values or objects without an 'id'
                model_ids = [model.id for model in models_response.data if hasattr(model, 'id') and model.id]
                if not model_ids:
                    print("Warning: Groq models list was empty or malformed.")
                    return []
                return sorted(model_ids) # Return sorted list
            else:
                raise APIError("Invalid response format from Groq models endpoint.", response=models_response) # Pass raw response if possible

        except AuthenticationError as e:
            raise ConnectionError(f"Groq authentication failed while listing models: Invalid API Key? ({e})") from e
        except APIConnectionError as e:
            raise ConnectionError(f"Groq API connection error while listing models: {e}") from e
        except APIError as e: # Catch other Groq API errors
             raise Exception(f"Groq API error listing models: Status={e.status_code}, Message={e.body.get('message', 'Unknown error') if e.body else 'Unknown error'}") from e
        except Exception as e:
            # Catch-all for unexpected errors
            raise Exception(f"An unexpected error occurred listing Groq models: {e}") from e

    def test_connection(self) -> bool:
        """
        Tests the connection by attempting to list models.

        Returns:
            True if listing models succeeds.

        Raises:
            ConnectionError: If the connection or authentication fails.
            Exception: For other errors during the test.
        """
        try:
            # Listing models tests authentication and basic connectivity
            self.list_models()
            return True
        except (ConnectionError, Exception) as e:
            # Re-raise the specific error encountered during the test
            raise e
