from abc import ABC, abstractmethod

class LLMInterface(ABC):
    """Abstract Base Class for Large Language Model interactions."""

    def __init__(self, api_key: str = None, model_id: str = None, api_endpoint: str = None):
        """
        Initializes the LLM interface. Specific implementations will use
        the necessary parameters (e.g., api_key for cloud services,
        api_endpoint or model_id for local).

        Args:
            api_key: API key for cloud-based LLMs.
            model_id: Identifier for the specific model to use.
            api_endpoint: Base URL for local LLM APIs (like Ollama).
        """
        self.api_key = api_key
        self.model_id = model_id
        self.api_endpoint = api_endpoint

    @abstractmethod
    def generate(self, prompt: str, max_tokens: int = 1500) -> str:
        """
        Generates text based on the provided prompt.

        Args:
            prompt: The input prompt for the LLM.
            max_tokens: The maximum number of tokens to generate (optional).

        Returns:
            The generated text as a string.

        Raises:
            Exception: If the generation fails.
        """
        pass

    @abstractmethod
    def list_models(self) -> list[str]:
        """
        Fetches a list of available model IDs from the provider.

        Returns:
            A list of model ID strings.

        Raises:
            Exception: If fetching the models fails.
        """
        pass

    @abstractmethod
    def test_connection(self) -> bool:
        """
        Tests the basic connection and authentication to the LLM service/endpoint.

        Returns:
            True if the connection is successful.

        Raises:
            Exception: If the connection test fails, providing details.
        """
        pass

# Import model lists from implementation files
from .llm_implementations.anthropic_llm import KNOWN_ANTHROPIC_MODELS
# Import config manager to load saved models
from . import config_manager
# Import necessary libraries for new providers
from groq import Groq, APIConnectionError, AuthenticationError
from openai import OpenAI, APIConnectionError as OpenAIConnectionError, AuthenticationError as OpenAIAuthenticationError
import os # For potential OpenRouter site name

# --- Available Models Registry ---
# This dictionary will be populated in three ways:
# 1. Initially with hardcoded model lists for providers that don't have model listing APIs
# 2. Loaded from previously saved successful model listings in config.ini
# 3. Updated by the settings window when keys are tested

# Start with empty lists for dynamic providers and hardcoded list for Anthropic
AVAILABLE_MODELS: dict[str, list[str]] = {
    "openai": [],
    "gemini": [],
    "anthropic": KNOWN_ANTHROPIC_MODELS,
    "openrouter": [], # Added
    "groq": [],       # Added
    "local": ["Default Local Model"]
}

# Load any previously saved models from config
saved_models = config_manager.get_all_saved_models()
for provider, models in saved_models.items():
    if provider in AVAILABLE_MODELS and models:  # Only update if we have saved models
        AVAILABLE_MODELS[provider] = models
        print(f"Loaded {len(models)} saved models for {provider} from config.")


# --- Factory Function (Optional but helpful) ---

# Import specific implementations
from .llm_implementations.openai_llm import OpenAILLM
from .llm_implementations.gemini_llm import GeminiLLM
from .llm_implementations.anthropic_llm import AnthropicLLM
from .llm_implementations.openrouter_llm import OpenRouterLLM # Added
from .llm_implementations.groq_llm import GroqLLM             # Added
from .llm_implementations.local_llm import LocalLLM

def get_llm_instance(llm_type: str, api_key: str = None, model_id: str = None, api_endpoint: str = None) -> LLMInterface:
    """
    Factory function to get an instance of a specific LLM implementation.

    Args:
        llm_type: The type of LLM ('openai', 'gemini', 'anthropic', 'local').
        api_key: API key (required for cloud LLMs).
        model_id: Model identifier (optional, can be set in config or default).
        api_endpoint: API endpoint (required for some local LLMs).

    Returns:
        An instance of a class implementing LLMInterface.

    Raises:
        ValueError: If the llm_type is unknown or required parameters are missing.
    """
    # Placeholder implementations - Replace with actual imports and instantiation
    if llm_type == 'openai':
        # Use the real implementation
        # API key check is handled within OpenAILLM constructor now if not provided
        return OpenAILLM(api_key=api_key, model_id=model_id)

    elif llm_type == 'gemini':
        # Use the real implementation
        # API key check is handled within GeminiLLM constructor
        return GeminiLLM(api_key=api_key, model_id=model_id)

    elif llm_type == 'anthropic':
        # Use the real implementation
        # API key check is handled within AnthropicLLM constructor
        return AnthropicLLM(api_key=api_key, model_id=model_id)

    elif llm_type == 'local':
        # Local LLM might use endpoint or model_id depending on setup
        # Use the real implementation
        return LocalLLM(api_endpoint=api_endpoint, model_id=model_id, api_key=api_key)

    elif llm_type == 'openrouter': # Added
        return OpenRouterLLM(api_key=api_key, model_id=model_id)

    elif llm_type == 'groq': # Added
        return GroqLLM(api_key=api_key, model_id=model_id)

    else:
        raise ValueError(f"Unknown LLM type: {llm_type}")
