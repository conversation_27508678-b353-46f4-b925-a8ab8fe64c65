import google.generativeai as genai
from google.api_core import exceptions as google_exceptions
from ..llm_interface import LLMInterface
import os
from typing import List

# Default model if not specified - find a suitable default
DEFAULT_GEMINI_MODEL = "gemini-1.5-flash-latest" # Or "gemini-pro"

class GeminiLLM(LLMInterface):
    """Google Gemini LLM implementation."""

    def __init__(self, api_key: str = None, model_id: str = None, api_endpoint: str = None):
        # api_endpoint is not used for Gemini API
        resolved_api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not resolved_api_key:
            raise ValueError("Gemini API key not provided via argument or environment variable.")

        super().__init__(api_key=resolved_api_key, model_id=model_id or DEFAULT_GEMINI_MODEL)
        try:
            genai.configure(api_key=self.api_key)
            # Initialize the client/model - model_id is used during generation
            self.model = genai.GenerativeModel(self.model_id)
            print(f"Gemini client configured. Default model for generation: {self.model_id}")
        except Exception as e:
            raise ConnectionError(f"Failed to configure Gemini client: {e}")

    def generate(self, prompt: str, max_tokens: int = 2048) -> str:
        """Generates text using the Gemini API."""
        if not self.model:
             raise ConnectionError("Gemini client/model not initialized.")

        try:
            # Note: Gemini uses 'max_output_tokens'. We might need to adjust the interface
            # or handle the parameter mapping here if max_tokens is critical.
            # For simplicity, we'll use the default max output tokens for now.
            # Consider adding generation_config if more control is needed.
            response = self.model.generate_content(prompt)

            # Handle potential blocks or safety issues if needed
            # See Gemini documentation for response structure and error handling
            if response.parts:
                return response.text # Accessing the text directly
            elif response.prompt_feedback and response.prompt_feedback.block_reason:
                 raise Exception(f"Gemini generation blocked: {response.prompt_feedback.block_reason}")
            else:
                 # Attempt to access text even if parts might be empty, handle potential errors
                 try:
                     return response.text
                 except ValueError as ve: # Handle cases like no response text
                     print(f"Gemini Warning: Could not extract text from response. {ve}")
                     print(f"Full Response: {response}")
                     return "" # Return empty string or raise a more specific error
                 except Exception as inner_e:
                     raise ValueError(f"Invalid or unexpected response structure received from Gemini: {inner_e}")


        except google_exceptions.GoogleAPIError as e:
            print(f"Gemini API error: {e}")
            # Check for specific permission denied/authentication errors
            if isinstance(e, (google_exceptions.PermissionDenied, google_exceptions.Unauthenticated)):
                 raise ConnectionError(f"Gemini Authentication Error: Invalid API Key? Details: {e}")
            raise Exception(f"Gemini API error: {e}") from e
        except Exception as e:
            print(f"Error during Gemini generation: {e}")
            raise Exception(f"Failed to generate text with Gemini: {e}") from e

    def test_connection(self) -> bool:
        """Tests the connection by attempting to list models."""
        if not self.api_key:
             raise ConnectionError("Gemini API key not configured.")
        try:
            # Listing models requires authentication, so it's a good test
            models = genai.list_models()
            # Check if we got at least one model back
            if any('generateContent' in m.supported_generation_methods for m in models):
                print("Gemini connection test successful (found usable models).")
                return True
            else:
                raise ConnectionError("Gemini connection test failed: No models supporting 'generateContent' found.")

        except (google_exceptions.PermissionDenied, google_exceptions.Unauthenticated) as e:
             print(f"Gemini connection test failed (Authentication Error): {e}")
             raise ConnectionError("Invalid API key provided. Please check your Gemini API key.")
        except google_exceptions.GoogleAPIError as e:
             print(f"Gemini connection test failed (API Error): {e}")
             raise ConnectionError(f"Gemini API error during connection test: {e}")
        except Exception as e:
            print(f"Gemini connection test failed (Unexpected Error): {e}")
            raise ConnectionError(f"Unexpected error during Gemini connection test: {e}")

    def list_models(self) -> List[str]:
        """Fetches a list of available model IDs from Gemini."""
        if not self.api_key:
            raise ConnectionError("Gemini API key not configured.")
        try:
            models = genai.list_models()
            # Filter for models that support content generation
            generative_models = [m.name.replace("models/", "") # Clean up the name
                                 for m in models
                                 if 'generateContent' in m.supported_generation_methods]
            print(f"Fetched {len(generative_models)} generative models from Gemini.")
            return sorted(generative_models)
        except (google_exceptions.PermissionDenied, google_exceptions.Unauthenticated):
             raise ConnectionError("Invalid API key provided. Cannot list models.")
        except google_exceptions.GoogleAPIError as e:
             raise Exception(f"Gemini API error while listing models: {e}") from e
        except Exception as e:
            raise Exception(f"Unexpected error listing Gemini models: {e}") from e

# Example usage (optional, for testing)
if __name__ == '__main__':
    print("Testing Gemini LLM Implementation...")
    # Assumes GEMINI_API_KEY is set as an environment variable

    try:
        print("\nTesting connection (using env key if set)...")
        gemini_llm = GeminiLLM() # Relies on env var
        if gemini_llm.test_connection():
            print("\nTesting model listing...")
            try:
                models = gemini_llm.list_models()
                print(f"Available models ({len(models)}):")
                for model in models[:10]: # Print first 10
                    print(f"- {model}")
                if len(models) > 10: print("...")
            except Exception as e:
                print(f"Failed to list models: {e}")

            print("\nTesting generation (using default model)...")
            test_prompt = "Explain the concept of a Large Language Model in one sentence."
            # Use the instance's model_id for generation
            gemini_llm.model = genai.GenerativeModel(gemini_llm.model_id)
            generated_text = gemini_llm.generate(test_prompt)
            print(f"Prompt: {test_prompt}")
            print(f"Generated Text: {generated_text}")
        else:
             print("Connection test returned False (should have raised exception on failure).")

    except ValueError as e:
         print(f"Skipping test: {e}") # Likely API key not found
    except ConnectionError as e:
         print(f"Connection test failed: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
