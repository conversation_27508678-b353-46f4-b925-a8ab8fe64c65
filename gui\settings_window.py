import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from core import config_manager
from core import serp_fetcher
from core import shopify_poster
from core import wordpress_poster # Import the new module
# Import the registry and the factory function
from core.llm_interface import get_llm_instance, AVAILABLE_MODELS

class SettingsWindow:
    def __init__(self, master):
        self.master = master
        master.title("Settings")
        # Make settings window modal
        master.grab_set()
        master.transient(master.master)
        master.geometry("700x750")

        # --- Main Frame ---
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Use Notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=5)

        # --- API Keys Tab ---
        api_keys_frame = ttk.Frame(notebook, padding="10")
        notebook.add(api_keys_frame, text="API Keys & Connections")
        self.create_api_keys_tab(api_keys_frame)

        # --- Prompts Tab ---
        prompts_frame = ttk.Frame(notebook, padding="10")
        notebook.add(prompts_frame, text="LLM Prompts")
        self.create_prompts_tab(prompts_frame)

        # --- Save Button ---
        save_button = ttk.Button(main_frame, text="Save Settings", command=self.save_settings)
        save_button.pack(pady=10)

        # --- Load initial settings ---
        self.load_settings()

    def create_api_keys_tab(self, parent_frame):
        """Creates the content for the API Keys tab."""
        parent_frame.columnconfigure(1, weight=1)

        row_index = 0

        # SERP API
        ttk.Label(parent_frame, text="SERP API Key:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.serpapi_key_var = tk.StringVar()
        serpapi_entry = ttk.Entry(parent_frame, textvariable=self.serpapi_key_var, width=50, show="*")
        serpapi_entry.grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=self.test_serpapi).grid(row=row_index, column=2, padx=5, pady=5)
        # Show/hide toggle
        self.serpapi_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(parent_frame, text="Show", variable=self.serpapi_show_var, 
                     command=lambda: self.toggle_key_visibility(serpapi_entry, self.serpapi_show_var)).grid(row=row_index, column=3, padx=5, pady=5)
        row_index += 1

        # OpenAI
        ttk.Label(parent_frame, text="OpenAI API Key:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.openai_key_var = tk.StringVar()
        openai_entry = ttk.Entry(parent_frame, textvariable=self.openai_key_var, width=50, show="*")
        openai_entry.grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=lambda: self.test_llm('openai')).grid(row=row_index, column=2, padx=5, pady=5)
        # Show/hide toggle
        self.openai_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(parent_frame, text="Show", variable=self.openai_show_var, 
                     command=lambda: self.toggle_key_visibility(openai_entry, self.openai_show_var)).grid(row=row_index, column=3, padx=5, pady=5)
        row_index += 1

        # Gemini
        ttk.Label(parent_frame, text="Gemini API Key:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.gemini_key_var = tk.StringVar()
        gemini_entry = ttk.Entry(parent_frame, textvariable=self.gemini_key_var, width=50, show="*")
        gemini_entry.grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=lambda: self.test_llm('gemini')).grid(row=row_index, column=2, padx=5, pady=5)
        # Show/hide toggle
        self.gemini_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(parent_frame, text="Show", variable=self.gemini_show_var, 
                     command=lambda: self.toggle_key_visibility(gemini_entry, self.gemini_show_var)).grid(row=row_index, column=3, padx=5, pady=5)
        row_index += 1

        # Anthropic
        ttk.Label(parent_frame, text="Anthropic API Key:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.anthropic_key_var = tk.StringVar()
        anthropic_entry = ttk.Entry(parent_frame, textvariable=self.anthropic_key_var, width=50, show="*")
        anthropic_entry.grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=lambda: self.test_llm('anthropic')).grid(row=row_index, column=2, padx=5, pady=5)
        # Show/hide toggle
        self.anthropic_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(parent_frame, text="Show", variable=self.anthropic_show_var,
                     command=lambda: self.toggle_key_visibility(anthropic_entry, self.anthropic_show_var)).grid(row=row_index, column=3, padx=5, pady=5)
        row_index += 1

        # OpenRouter
        ttk.Label(parent_frame, text="OpenRouter API Key:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.openrouter_key_var = tk.StringVar()
        openrouter_entry = ttk.Entry(parent_frame, textvariable=self.openrouter_key_var, width=50, show="*")
        openrouter_entry.grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=lambda: self.test_llm('openrouter')).grid(row=row_index, column=2, padx=5, pady=5)
        # Show/hide toggle
        self.openrouter_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(parent_frame, text="Show", variable=self.openrouter_show_var,
                     command=lambda: self.toggle_key_visibility(openrouter_entry, self.openrouter_show_var)).grid(row=row_index, column=3, padx=5, pady=5)
        row_index += 1

        # Groq
        ttk.Label(parent_frame, text="Groq API Key:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.groq_key_var = tk.StringVar()
        groq_entry = ttk.Entry(parent_frame, textvariable=self.groq_key_var, width=50, show="*")
        groq_entry.grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=lambda: self.test_llm('groq')).grid(row=row_index, column=2, padx=5, pady=5)
        # Show/hide toggle
        self.groq_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(parent_frame, text="Show", variable=self.groq_show_var,
                     command=lambda: self.toggle_key_visibility(groq_entry, self.groq_show_var)).grid(row=row_index, column=3, padx=5, pady=5)
        row_index += 1

        # Local LLM
        ttk.Label(parent_frame, text="Local LLM API URL:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.local_llm_endpoint_var = tk.StringVar(value="http://localhost:1234/v1")
        ttk.Entry(parent_frame, textvariable=self.local_llm_endpoint_var, width=50).grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(parent_frame, text="Test", command=lambda: self.test_llm('local')).grid(row=row_index, column=2, padx=5, pady=5)
        row_index += 1
        ttk.Label(parent_frame, text="(For LMStudio, use 'http://localhost:1234/v1')").grid(row=row_index, column=1, padx=5, pady=0, sticky=tk.W)
        row_index += 1
        
        # Local LLM Model ID (optional)
        ttk.Label(parent_frame, text="Local LLM Model ID:").grid(row=row_index, column=0, padx=5, pady=5, sticky=tk.W)
        self.local_llm_model_var = tk.StringVar()
        ttk.Entry(parent_frame, textvariable=self.local_llm_model_var, width=50).grid(row=row_index, column=1, padx=5, pady=5, sticky=tk.EW)
        row_index += 1
        ttk.Label(parent_frame, text="(Optional - leave blank to use server default)").grid(row=row_index, column=1, padx=5, pady=0, sticky=tk.W)
        row_index += 1

        # Removed duplicate Shopify fields from the API Keys section
        # These are now handled exclusively in the Shopify Platform tab
        row_index += 0

        # --- Separator ---
        ttk.Separator(parent_frame, orient='horizontal').grid(row=row_index, column=0, columnspan=4, sticky='ew', pady=15)
        row_index += 1

        # --- Platform Posting Settings Notebook ---
        ttk.Label(parent_frame, text="Platform Posting Settings:", font="-weight bold").grid(row=row_index, column=0, columnspan=4, padx=5, pady=(0,5), sticky=tk.W)
        row_index += 1

        platform_notebook = ttk.Notebook(parent_frame)
        platform_notebook.grid(row=row_index, column=0, columnspan=4, sticky='nsew', padx=5, pady=5)
        parent_frame.rowconfigure(row_index, weight=1) # Allow notebook to expand if needed
        row_index += 1

        # --- Shopify Platform Tab ---
        shopify_platform_frame = ttk.Frame(platform_notebook, padding="10")
        platform_notebook.add(shopify_platform_frame, text="Shopify")
        shopify_platform_frame.columnconfigure(1, weight=1)

        sp_row = 0
        # Shopify URL (Moved)
        ttk.Label(shopify_platform_frame, text="Store URL:").grid(row=sp_row, column=0, padx=5, pady=5, sticky=tk.W)
        self.shopify_store_url_var = tk.StringVar()
        ttk.Entry(shopify_platform_frame, textvariable=self.shopify_store_url_var, width=40).grid(row=sp_row, column=1, padx=5, pady=5, sticky=tk.EW)
        sp_row += 1
        ttk.Label(shopify_platform_frame, text="(e.g., your-store.myshopify.com)").grid(row=sp_row, column=1, padx=5, pady=0, sticky=tk.W)
        sp_row += 1

        # Shopify Token (Moved)
        ttk.Label(shopify_platform_frame, text="Admin API Token:").grid(row=sp_row, column=0, padx=5, pady=5, sticky=tk.W)
        self.shopify_api_token_var = tk.StringVar()
        shopify_token_entry_tab = ttk.Entry(shopify_platform_frame, textvariable=self.shopify_api_token_var, width=40, show="*")
        shopify_token_entry_tab.grid(row=sp_row, column=1, padx=5, pady=5, sticky=tk.EW)
        # Show/hide toggle (Moved)
        self.shopify_token_show_var_tab = tk.BooleanVar(value=False)
        ttk.Checkbutton(shopify_platform_frame, text="Show", variable=self.shopify_token_show_var_tab,
                     command=lambda: self.toggle_key_visibility(shopify_token_entry_tab, self.shopify_token_show_var_tab)).grid(row=sp_row, column=2, padx=5, pady=5)
        sp_row += 1

        # Shopify Test Button & Status (Moved/Modified)
        shopify_test_frame = ttk.Frame(shopify_platform_frame)
        shopify_test_frame.grid(row=sp_row, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Button(shopify_test_frame, text="Test Shopify Connection", command=self.test_shopify).pack(side=tk.LEFT, padx=5)
        self.shopify_status_label_var = tk.StringVar(value="Status: Untested")
        ttk.Label(shopify_test_frame, textvariable=self.shopify_status_label_var).pack(side=tk.LEFT, padx=5)
        sp_row += 1

        # Shopify Upload Meta Checkbox
        self.upload_meta_shopify_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(shopify_platform_frame, text="Upload Meta Description when posting to Shopify", variable=self.upload_meta_shopify_var).grid(row=sp_row, column=0, columnspan=3, padx=5, pady=5, sticky=tk.W)
        sp_row += 1


        # --- WordPress Platform Tab ---
        wp_platform_frame = ttk.Frame(platform_notebook, padding="10")
        platform_notebook.add(wp_platform_frame, text="WordPress")
        wp_platform_frame.columnconfigure(1, weight=1)

        wp_row = 0
        # WordPress Site URL
        ttk.Label(wp_platform_frame, text="Site URL:").grid(row=wp_row, column=0, padx=5, pady=5, sticky=tk.W)
        self.wp_site_url_var = tk.StringVar()
        ttk.Entry(wp_platform_frame, textvariable=self.wp_site_url_var, width=40).grid(row=wp_row, column=1, padx=5, pady=5, sticky=tk.EW)
        wp_row += 1
        ttk.Label(wp_platform_frame, text="(e.g., https://yourblog.com)").grid(row=wp_row, column=1, padx=5, pady=0, sticky=tk.W)
        wp_row += 1

        # WordPress Username
        ttk.Label(wp_platform_frame, text="Username:").grid(row=wp_row, column=0, padx=5, pady=5, sticky=tk.W)
        self.wp_username_var = tk.StringVar()
        ttk.Entry(wp_platform_frame, textvariable=self.wp_username_var, width=40).grid(row=wp_row, column=1, padx=5, pady=5, sticky=tk.EW)
        wp_row += 1

        # WordPress Application Password
        ttk.Label(wp_platform_frame, text="Application Password:").grid(row=wp_row, column=0, padx=5, pady=5, sticky=tk.W)
        self.wp_app_password_var = tk.StringVar()
        wp_app_password_entry = ttk.Entry(wp_platform_frame, textvariable=self.wp_app_password_var, width=40, show="*")
        wp_app_password_entry.grid(row=wp_row, column=1, padx=5, pady=5, sticky=tk.EW)
        # Show/hide toggle
        self.wp_app_password_show_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(wp_platform_frame, text="Show", variable=self.wp_app_password_show_var,
                     command=lambda: self.toggle_key_visibility(wp_app_password_entry, self.wp_app_password_show_var)).grid(row=wp_row, column=2, padx=5, pady=5)
        wp_row += 1
        ttk.Label(wp_platform_frame, text="(Recommended: Generate via WP Admin > Users > Profile)").grid(row=wp_row, column=1, padx=5, pady=0, sticky=tk.W)
        wp_row += 1

        # WordPress Test Button & Status
        wp_test_frame = ttk.Frame(wp_platform_frame)
        wp_test_frame.grid(row=wp_row, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Button(wp_test_frame, text="Test WordPress Connection", command=self.test_wordpress).pack(side=tk.LEFT, padx=5)
        self.wp_status_label_var = tk.StringVar(value="Status: Untested")
        ttk.Label(wp_test_frame, textvariable=self.wp_status_label_var).pack(side=tk.LEFT, padx=5)
        wp_row += 1

        # WordPress Upload Meta Checkbox
        self.upload_meta_wordpress_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(wp_platform_frame, text="Upload Meta Description when posting to WordPress*", variable=self.upload_meta_wordpress_var).grid(row=wp_row, column=0, columnspan=3, padx=5, pady=5, sticky=tk.W)
        wp_row += 1
        ttk.Label(wp_platform_frame, text="*Requires compatible SEO plugin (e.g., Yoast, RankMath) on WordPress site.").grid(row=wp_row, column=0, columnspan=3, padx=5, pady=0, sticky=tk.W)
        wp_row += 1


    def create_prompts_tab(self, parent_frame):
        """Creates the content for the LLM Prompts tab."""
        # Configure rows/columns for prompts
        parent_frame.columnconfigure(0, weight=1)
        parent_frame.rowconfigure(1, weight=1) # Analysis
        parent_frame.rowconfigure(3, weight=1) # Gatekeeper
        parent_frame.rowconfigure(5, weight=1) # Writing
        parent_frame.rowconfigure(7, weight=1) # Critic
        parent_frame.rowconfigure(9, weight=1) # Keyword Fallback (New)

        row_idx = 0
        # Analysis Prompt
        ttk.Label(parent_frame, text="LLM Analysis Prompt:").grid(row=row_idx, column=0, padx=5, pady=(5,0), sticky=tk.W)
        row_idx += 1
        self.analysis_prompt_text = scrolledtext.ScrolledText(parent_frame, wrap=tk.WORD, height=8) # Adjusted height
        self.analysis_prompt_text.grid(row=row_idx, column=0, padx=5, pady=(0,5), sticky=tk.NSEW)
        row_idx += 1

        # Gatekeeper Prompt (New)
        ttk.Label(parent_frame, text="LLM Gatekeeper Prompt:").grid(row=row_idx, column=0, padx=5, pady=(10,0), sticky=tk.W)
        row_idx += 1
        self.gatekeeper_prompt_text = scrolledtext.ScrolledText(parent_frame, wrap=tk.WORD, height=8) # Adjusted height
        self.gatekeeper_prompt_text.grid(row=row_idx, column=0, padx=5, pady=(0,5), sticky=tk.NSEW)
        row_idx += 1

        # Writing Prompt
        ttk.Label(parent_frame, text="LLM Blog Writing Prompt:").grid(row=row_idx, column=0, padx=5, pady=(10,0), sticky=tk.W)
        row_idx += 1
        self.writing_prompt_text = scrolledtext.ScrolledText(parent_frame, wrap=tk.WORD, height=8) # Adjusted height
        self.writing_prompt_text.grid(row=row_idx, column=0, padx=5, pady=(0,5), sticky=tk.NSEW)
        row_idx += 1

        # Critic Prompt
        ttk.Label(parent_frame, text="LLM Blog Critic Prompt:").grid(row=row_idx, column=0, padx=5, pady=(10,0), sticky=tk.W)
        row_idx += 1
        self.critic_prompt_text = scrolledtext.ScrolledText(parent_frame, wrap=tk.WORD, height=8) # Adjusted height
        self.critic_prompt_text.grid(row=row_idx, column=0, padx=5, pady=(0,5), sticky=tk.NSEW)
        row_idx += 1

        # Keyword Fallback Prompt (New)
        ttk.Label(parent_frame, text="LLM Keyword/PAA Fallback Prompt:").grid(row=row_idx, column=0, padx=5, pady=(10,0), sticky=tk.W)
        row_idx += 1
        self.keyword_fallback_prompt_text = scrolledtext.ScrolledText(parent_frame, wrap=tk.WORD, height=8) # Adjusted height
        self.keyword_fallback_prompt_text.grid(row=row_idx, column=0, padx=5, pady=(0,5), sticky=tk.NSEW)
        row_idx += 1

        # Add variable reference button
        var_frame = ttk.Frame(parent_frame)
        var_frame.grid(row=row_idx, column=0, padx=5, pady=(10,5), sticky=tk.W)
        ttk.Button(var_frame, text="Show Prompt Variables", command=self.show_prompt_variables).pack(side=tk.LEFT, padx=5)

    def load_settings(self):
        """Loads settings from config_manager into the GUI fields."""
        api_keys = config_manager.get_all_api_keys()
        prompts = config_manager.get_all_prompts()

        self.serpapi_key_var.set(api_keys.get('serpapi_key', ''))
        self.openai_key_var.set(api_keys.get('openai_key', ''))
        self.gemini_key_var.set(api_keys.get('gemini_key', ''))
        self.anthropic_key_var.set(api_keys.get('anthropic_key', ''))
        self.openrouter_key_var.set(api_keys.get('openrouter_key', '')) # Added
        self.groq_key_var.set(api_keys.get('groq_key', ''))             # Added
        self.local_llm_endpoint_var.set(api_keys.get('local_llm_endpoint', 'http://localhost:1234/v1'))
        self.local_llm_model_var.set(api_keys.get('local_llm_model', ''))
        # Load Shopify Platform Settings
        # First check the old config structure (API_KEYS section)
        shopify_token = config_manager.get_api_key('shopify_api_token') or ''
        shopify_url = config_manager.get_api_key('shopify_store_url') or ''
        
        # Then check the new structure and override if values exist there
        if config_manager.get_setting('SHOPIFY', 'api_token', ''):
            shopify_token = config_manager.get_setting('SHOPIFY', 'api_token', '')
        if config_manager.get_setting('SHOPIFY', 'shop_url', ''):
            shopify_url = config_manager.get_setting('SHOPIFY', 'shop_url', '')
            
        self.shopify_api_token_var.set(shopify_token)
        self.shopify_store_url_var.set(shopify_url)
        shopify_ok = config_manager.get_setting('SHOPIFY', 'connection_ok', 'False').lower() == 'true'
        self.shopify_status_label_var.set("Status: Connected" if shopify_ok else "Status: Untested/Failed")
        upload_meta_shopify = config_manager.get_setting('SHOPIFY', 'upload_meta_description', 'False').lower() == 'true'
        self.upload_meta_shopify_var.set(upload_meta_shopify)

        # Load WordPress Platform Settings
        self.wp_site_url_var.set(config_manager.get_setting('WORDPRESS', 'site_url', ''))
        self.wp_username_var.set(config_manager.get_setting('WORDPRESS', 'username', ''))
        self.wp_app_password_var.set(config_manager.get_setting('WORDPRESS', 'app_password', ''))
        wp_ok = config_manager.get_setting('WORDPRESS', 'connection_ok', 'False').lower() == 'true'
        self.wp_status_label_var.set("Status: Connected" if wp_ok else "Status: Untested/Failed")
        upload_meta_wp = config_manager.get_setting('WORDPRESS', 'upload_meta_description', 'False').lower() == 'true'
        self.upload_meta_wordpress_var.set(upload_meta_wp)

        # Add traces AFTER loading initial values to prevent premature resets
        self.shopify_store_url_var.trace_add("write", self._reset_shopify_status)
        self.shopify_api_token_var.trace_add("write", self._reset_shopify_status)
        self.wp_site_url_var.trace_add("write", self._reset_wp_status)
        self.wp_username_var.trace_add("write", self._reset_wp_status)
        self.wp_app_password_var.trace_add("write", self._reset_wp_status)


        self.analysis_prompt_text.delete('1.0', tk.END)
        self.analysis_prompt_text.insert('1.0', prompts.get('analysis_prompt', '')) # Default handled in module
        self.gatekeeper_prompt_text.delete('1.0', tk.END)
        self.gatekeeper_prompt_text.insert('1.0', prompts.get('gatekeeper_prompt', '')) # Default handled in module
        self.writing_prompt_text.delete('1.0', tk.END)
        self.writing_prompt_text.insert('1.0', prompts.get('writing_prompt', '')) # Default handled in module
        self.critic_prompt_text.delete('1.0', tk.END)
        self.critic_prompt_text.insert('1.0', prompts.get('critic_prompt', '')) # Default handled in module
        self.keyword_fallback_prompt_text.delete('1.0', tk.END)
        self.keyword_fallback_prompt_text.insert('1.0', prompts.get('keyword_fallback_prompt', '')) # Default handled in main_window for now

    def show_prompt_variables(self):
        """Displays a popup with all available prompt variables."""
        variable_info = """Available Prompt Variables:

{keyword} - The original seed keyword
{serp_data} - Raw SERP data JSON (for Analysis)
{analysis_results} - Output from the Analysis AI (for Writing, Critic, Gatekeeper)
{proposed_content} - Alias for {analysis_results} (specifically for Gatekeeper prompt)
{existing_posts} - Formatted list of existing post titles/snippets (for Gatekeeper prompt)
{gatekeeper_feedback} - Output from the Gatekeeper AI (for Writing prompt)
{blog_content} - Generated blog content HTML (for Critic)
{target_word_count} - Target word count specified (for Writing, Critic)
{actual_word_count} - Actual word count of generated blog (for Critic)
{tone} - Specified writing tone (for Writing, Critic)

--- Typical Usage ---

Keyword Fallback Prompt: {keyword}

Analysis Prompt: {keyword}, {serp_data}

Gatekeeper Prompt: {keyword}, {proposed_content} (or {analysis_results}), {existing_posts}

Writing Prompt: {keyword}, {analysis_results}, {word_count}, {tone}, {gatekeeper_feedback}

Critic Prompt: {keyword}, {serp_data}, {analysis_results}, {blog_content}, {target_word_count}, {actual_word_count}, {tone}
"""

        # Create popup window
        popup = tk.Toplevel(self.master)
        popup.title("Prompt Variables Reference")
        popup.geometry("600x400")
        
        # Make it modal
        popup.transient(self.master)
        popup.grab_set()
        
        # Add text area with the variable info
        var_text = scrolledtext.ScrolledText(popup, wrap=tk.WORD)
        var_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        var_text.insert('1.0', variable_info)
        var_text.config(state=tk.DISABLED)  # Make it read-only
        
        # Add close button
        ttk.Button(popup, text="Close", command=popup.destroy).pack(pady=10)

    def save_settings(self):
        """Saves the current GUI field values back to config.ini."""
        settings_to_update = {
            'API_KEYS.serpapi_key': self.serpapi_key_var.get(),
            'API_KEYS.openai_key': self.openai_key_var.get(),
            'API_KEYS.gemini_key': self.gemini_key_var.get(),
            'API_KEYS.anthropic_key': self.anthropic_key_var.get(),
            'API_KEYS.openrouter_key': self.openrouter_key_var.get(), # Added
            'API_KEYS.groq_key': self.groq_key_var.get(),             # Added
            'API_KEYS.local_llm_endpoint': self.local_llm_endpoint_var.get(),
            'API_KEYS.local_llm_model': self.local_llm_model_var.get(),
            # Remove the old keys from API_KEYS section to avoid duplication
            'API_KEYS.shopify_api_token': None,
            'API_KEYS.shopify_store_url': None,
            # Save Platform Credentials (Note: connection_ok is saved only on test)
            'SHOPIFY.shop_url': self.shopify_store_url_var.get(),
            'SHOPIFY.api_token': self.shopify_api_token_var.get(),
            'SHOPIFY.upload_meta_description': str(self.upload_meta_shopify_var.get()),
            'WORDPRESS.site_url': self.wp_site_url_var.get(),
            'WORDPRESS.username': self.wp_username_var.get(),
            'WORDPRESS.app_password': self.wp_app_password_var.get(),
            'WORDPRESS.upload_meta_description': str(self.upload_meta_wordpress_var.get()),
            # Save Prompts
            'PROMPTS.analysis_prompt': self.analysis_prompt_text.get('1.0', tk.END).strip(),
            'PROMPTS.gatekeeper_prompt': self.gatekeeper_prompt_text.get('1.0', tk.END).strip(), # Added
            'PROMPTS.writing_prompt': self.writing_prompt_text.get('1.0', tk.END).strip(),
            'PROMPTS.critic_prompt': self.critic_prompt_text.get('1.0', tk.END).strip(),
            'PROMPTS.keyword_fallback_prompt': self.keyword_fallback_prompt_text.get('1.0', tk.END).strip(), # Added
        }
        # Remove sensitive keys if empty to avoid saving blank passwords etc.
        keys_to_remove_if_empty = [
            'SHOPIFY.api_token',
            'WORDPRESS.app_password'
        ]
        final_settings = {k: v for k, v in settings_to_update.items()
                          if not (k in keys_to_remove_if_empty and not v)}

        try:
            config_manager.update_multiple_settings(final_settings)
            messagebox.showinfo("Settings Saved", "Settings have been saved successfully.", parent=self.master)
        except Exception as e:
            messagebox.showerror("Error Saving Settings", f"Failed to save settings: {e}", parent=self.master)

    def toggle_key_visibility(self, entry_widget, show_var):
        """Toggles the visibility of API keys in entry fields."""
        if show_var.get():
            entry_widget.config(show="")
        else:
            entry_widget.config(show="*")

    def test_serpapi(self):
        """Tests the SERP API connection with the provided key."""
        api_key = self.serpapi_key_var.get()
        if not api_key or 'YOUR_' in api_key:
            messagebox.showwarning("Test Connection", "Please enter a valid SERP API key.", parent=self.master)
            return
        try:
            success = serp_fetcher.test_connection(api_key)
            if success:
                messagebox.showinfo("Test Connection", "SERP API connection successful! Your API key is working properly.", parent=self.master)
            else:
                messagebox.showerror("Test Connection", "SERP API connection test returned unexpected result.", parent=self.master)
        except Exception as e:
            error_msg = str(e)
            if "Invalid API key" in error_msg:
                error_msg = "Invalid API key provided. Please check your SerpAPI key."
            messagebox.showerror("Test Connection Error", f"SERP API connection failed:\n{error_msg}", parent=self.master)

    def test_llm(self, llm_type):
        """Tests the connection to the specified LLM provider."""
        # Get the appropriate key or endpoint
        if llm_type == 'openai': 
            api_key = self.openai_key_var.get()
            if not api_key:
                messagebox.showwarning("Test Connection", "Please enter an OpenAI API key.", parent=self.master)
                return
        elif llm_type == 'gemini': 
            api_key = self.gemini_key_var.get()
            if not api_key:
                messagebox.showwarning("Test Connection", "Please enter a Gemini API key.", parent=self.master)
                return
        elif llm_type == 'anthropic': 
            api_key = self.anthropic_key_var.get()
            if not api_key:
                messagebox.showwarning("Test Connection", "Please enter an Anthropic API key.", parent=self.master)
                return
        elif llm_type == 'openrouter': # Added
            api_key = self.openrouter_key_var.get()
            if not api_key:
                messagebox.showwarning("Test Connection", "Please enter an OpenRouter API key.", parent=self.master)
                return
        elif llm_type == 'groq': # Added
            api_key = self.groq_key_var.get()
            if not api_key:
                messagebox.showwarning("Test Connection", "Please enter a Groq API key.", parent=self.master)
                return
        elif llm_type == 'local':
            api_key = None
            api_endpoint = self.local_llm_endpoint_var.get()
            model_id = self.local_llm_model_var.get() or None
            if not api_endpoint:
                messagebox.showwarning("Test Connection", "Please enter a Local LLM API URL.", parent=self.master)
                return

        try:
            # Create LLM instance
            if llm_type == 'local':
                llm_instance = get_llm_instance(
                    llm_type=llm_type,
                    api_endpoint=api_endpoint,
                    model_id=model_id
                )
            else:
                llm_instance = get_llm_instance(
                    llm_type=llm_type,
                    api_key=api_key
                )
            
            # --- Test Connection ---
            print(f"Testing {llm_type.capitalize()} connection...")
            connection_ok = llm_instance.test_connection() # This will raise ConnectionError on failure

            if not connection_ok:
                 # Should not happen if test_connection raises exception on failure
                 raise ConnectionError("Connection test returned False unexpectedly.")

            print(f"{llm_type.capitalize()} connection successful. Testing model listing...")

            # --- List Models ---
            models = llm_instance.list_models() # This can also raise exceptions

            # --- Update Registry and Show Success ---
            if models:
                AVAILABLE_MODELS[llm_type] = models
                print(f"Successfully fetched {len(models)} models for {llm_type}. Updated registry.")
                # Save models to config file for persistence
                config_manager.save_models_list(llm_type, models)
                messagebox.showinfo("Test Connection Successful",
                                   f"{llm_type.capitalize()} API connection successful!\n"
                                   f"Fetched {len(models)} available models.",
                                   parent=self.master)
            else:
                 # This case might happen if list_models returns empty list without error
                 AVAILABLE_MODELS[llm_type] = [] # Ensure it's empty
                 messagebox.showwarning("Test Connection Warning",
                                       f"{llm_type.capitalize()} API connection successful, but no models were found or returned.",
                                       parent=self.master)

        except ConnectionError as e:
            # Don't clear models for anthropic as they're hardcoded
            if llm_type != 'anthropic':
                # Clear models for this type if connection fails
                AVAILABLE_MODELS[llm_type] = []
                print(f"Clearing models for {llm_type} due to connection error.")
            messagebox.showerror("Test Connection Error", f"{llm_type.capitalize()} connection failed:\n{e}", parent=self.master)
        except ValueError as e: # Catch missing API key errors from get_llm_instance
             messagebox.showerror("Configuration Error", f"Failed to initialize {llm_type.capitalize()} LLM:\n{e}", parent=self.master)
        except Exception as e:
            # Handle potential errors during list_models or other unexpected issues
            # Don't clear models for anthropic as they're hardcoded
            if llm_type != 'anthropic':
                # Clear models for this type on other errors
                AVAILABLE_MODELS[llm_type] = []
                print(f"Clearing models for {llm_type} due to unexpected error: {e}")
            error_msg = str(e)
            # Make error message slightly more specific if possible
            if "list_models" in error_msg:
                 messagebox.showerror("Model Listing Error",
                                      f"{llm_type.capitalize()} connection OK, but failed to list models:\n{error_msg}",
                                      parent=self.master)
            else:
                 # General fallback error
                 if "api key" in error_msg.lower() or "authentication" in error_msg.lower():
                     error_msg = f"Invalid API key. Please check your {llm_type.capitalize()} API key."
                 messagebox.showerror("Test Connection Error", f"{llm_type.capitalize()} test failed:\n{error_msg}", parent=self.master)


    def test_shopify(self):
        """Tests the Shopify connection with the provided credentials."""
        shop_url = self.shopify_store_url_var.get()
        api_token = self.shopify_api_token_var.get()
        
        if not shop_url or not api_token:
            messagebox.showwarning("Test Connection", "Please enter both a Shopify Store URL and Admin API Token.", parent=self.master)
            return
            
        try:
            success = shopify_poster.test_connection(shop_url=shop_url, api_token=api_token)
            if success:
                self.shopify_status_label_var.set("Status: Connected")
                messagebox.showinfo("Test Connection", "Shopify connection successful!", parent=self.master)
                # Save credentials and status on success
                config_manager.set_setting('SHOPIFY', 'shop_url', shop_url)
                config_manager.set_setting('SHOPIFY', 'api_token', api_token)
                config_manager.set_setting('SHOPIFY', 'connection_ok', 'True')
            else:
                # Should not happen if test_connection raises error
                self.shopify_status_label_var.set("Status: Failed (Unknown)")
                messagebox.showerror("Test Connection", "Shopify connection test failed unexpectedly.", parent=self.master)
                config_manager.set_setting('SHOPIFY', 'connection_ok', 'False')
        except Exception as e:
            self.shopify_status_label_var.set(f"Status: Failed")
            messagebox.showerror("Test Connection Error", f"Shopify connection failed:\n{e}", parent=self.master)
            config_manager.set_setting('SHOPIFY', 'connection_ok', 'False')

    def test_wordpress(self):
        """Tests the WordPress connection."""
        site_url = self.wp_site_url_var.get()
        username = self.wp_username_var.get()
        app_password = self.wp_app_password_var.get()

        if not site_url or not username or not app_password:
            messagebox.showwarning("Test Connection", "Please enter Site URL, Username, and Application Password.", parent=self.master)
            return

        self.wp_status_label_var.set("Status: Testing...")
        self.master.update_idletasks()

        try:
            # Call the actual test function
            success = wordpress_poster.test_connection(site_url, username, app_password)

            if success:
                self.wp_status_label_var.set("Status: Connected")
                messagebox.showinfo("Test Connection", "WordPress connection successful!", parent=self.master)
                config_manager.set_setting('WORDPRESS', 'site_url', site_url)
                config_manager.set_setting('WORDPRESS', 'username', username)
                config_manager.set_setting('WORDPRESS', 'app_password', app_password)
                config_manager.set_setting('WORDPRESS', 'connection_ok', 'True')
            else:
                # If test_connection raises error, this won't be reached
                self.wp_status_label_var.set("Status: Failed (Test Incomplete)")
                # messagebox.showerror("Test Connection", "WordPress connection test failed.", parent=self.master) # Covered by exception
                config_manager.set_setting('WORDPRESS', 'connection_ok', 'False')
        except Exception as e:
            self.wp_status_label_var.set(f"Status: Failed")
            messagebox.showerror("Test Connection Error", f"WordPress connection failed:\n{e}", parent=self.master)
            config_manager.set_setting('WORDPRESS', 'connection_ok', 'False')

    def _reset_shopify_status(self, *args):
        """Resets Shopify connection status when credentials change."""
        self.shopify_status_label_var.set("Status: Untested")
        config_manager.set_setting('SHOPIFY', 'connection_ok', 'False')

    def _reset_wp_status(self, *args):
        """Resets WordPress connection status when credentials change."""
        self.wp_status_label_var.set("Status: Untested")
        config_manager.set_setting('WORDPRESS', 'connection_ok', 'False')


if __name__ == '__main__':
    # This allows running this file directly for testing the layout
    root = tk.Tk()
    app = SettingsWindow(root)
    root.mainloop()
