import json
from .llm_interface import get_llm_instance
from . import shopify_poster
from . import wordpress_poster
from . import config_manager

# Constants (can be moved to config or made parameters)
MAX_POSTS_TO_FETCH = 50 # Limit how many recent posts to check

def fetch_existing_posts(platform: str, limit: int = MAX_POSTS_TO_FETCH) -> list[dict]:
    """
    Fetches recent post titles and snippets from the specified platform.

    Args:
        platform: The platform ('Shopify' or 'WordPress').
        limit: The maximum number of recent posts to fetch.

    Returns:
        A list of dictionaries, each containing 'title' and 'snippet' (or 'summary').
        Returns an empty list on error or if platform is unsupported/not configured.
    """
    existing_posts_summary = []
    try:
        config = config_manager.load_config()
        if platform == "Shopify":
            shop_url = config.get('SHOPIFY', 'shop_url', fallback=None)
            api_token = config.get('SHOPIFY', 'api_token', fallback=None)
            if not shop_url or not api_token:
                print("Gatekeeper: Shopify credentials not configured.")
                return []
            # Assuming shopify_poster has a function like fetch_recent_posts
            # We will need to add this function to shopify_poster.py
            recent_posts = shopify_poster.fetch_recent_posts(shop_url, api_token, limit=limit)
            for post in recent_posts:
                 # Extract title and a snippet (e.g., start of body_html, needs refinement)
                 snippet = post.get('summary_html') or post.get('body_html', '')
                 # Basic HTML strip for snippet
                 import re
                 snippet = re.sub('<[^<]+?>', ' ', snippet)[:250] + '...' if snippet else '' # Limit snippet length
                 existing_posts_summary.append({
                     "title": post.get('title', 'No Title'),
                     "snippet": snippet.strip()
                 })

        elif platform == "WordPress":
            site_url = config.get('WORDPRESS', 'site_url', fallback=None)
            username = config.get('WORDPRESS', 'username', fallback=None)
            app_password = config.get('WORDPRESS', 'app_password', fallback=None)
            if not site_url or not username or not app_password:
                print("Gatekeeper: WordPress credentials not configured.")
                return []
            # Assuming wordpress_poster has a function like fetch_recent_posts
            # We will need to add this function to wordpress_poster.py
            recent_posts = wordpress_poster.fetch_recent_posts(site_url, username, app_password, limit=limit)
            for post in recent_posts:
                 # Extract title and snippet (WP uses 'excerpt' often)
                 title = post.get('title', {}).get('rendered', 'No Title')
                 snippet = post.get('excerpt', {}).get('rendered', '')
                 # Basic HTML strip for snippet
                 import re
                 snippet = re.sub('<[^<]+?>', ' ', snippet)[:250] + '...' if snippet else '' # Limit snippet length
                 existing_posts_summary.append({
                     "title": title,
                     "snippet": snippet.strip()
                 })
        else:
            print(f"Gatekeeper: Unsupported platform '{platform}' for fetching posts.")
            return []

    except ImportError:
         print(f"Gatekeeper: Failed to import poster module for {platform}.")
         # Handle cases where poster modules might be missing dependencies
    except AttributeError:
         print(f"Gatekeeper: Function 'fetch_recent_posts' not found in {platform}_poster.py. Needs implementation.")
         # Handle missing function gracefully
    except Exception as e:
        print(f"Gatekeeper: Error fetching posts from {platform}: {e}")
        # Log error but return empty list to allow pipeline to potentially continue
        # Ensure empty list is returned on any error during fetch
        return []

    # Ensure empty list is returned if platform is unsupported
    if not existing_posts_summary:
         print(f"Gatekeeper: No posts fetched or platform '{platform}' not supported/configured correctly.")
         return []

    return existing_posts_summary


def run_gatekeeper_check(keyword: str, analysis_results: dict, platform: str, llm_type: str, model_id: str = None) -> str:
    """
    Performs the Gatekeeper check against existing content.
    This function checks for topic overlap BEFORE writing the blog post.

    Args:
        keyword: The seed keyword for the new post.
        analysis_results: The dictionary output from the llm_analyzer step.
        platform: The target platform ('Shopify' or 'WordPress') to check against.
        llm_type: The LLM provider type for the Gatekeeper.
        model_id: The specific LLM model ID for the Gatekeeper.

    Returns:
        A string containing feedback/instructions for the blog writer,
        or an empty string if no issues are found or an error occurs.
    """
    print(f"--- Running Gatekeeper Check for '{keyword}' against {platform} ---")
    feedback = ""
    try:
        # 1. Fetch existing post summaries
        print(f"Gatekeeper: Fetching existing posts from {platform}...")
        existing_posts = fetch_existing_posts(platform)
        if not existing_posts:
            print("Gatekeeper: No existing posts found or fetched. Skipping check.")
            return "" # No posts to compare against

        print(f"Gatekeeper: Found {len(existing_posts)} existing post summaries to compare against.")

        # 2. Prepare input for Gatekeeper LLM
        # Extract proposed headings/topics from analysis_results (structure might vary)
        proposed_content_summary = analysis_results.get('raw_analysis', 'No analysis available.') # Use raw analysis for now

        # Format existing posts for the prompt
        existing_posts_formatted = "\n".join([f"- Title: {post['title']}\n  Snippet: {post['snippet']}" for post in existing_posts])

        # 3. Construct the prompt
        # Load base prompt from config if available, otherwise use default
        prompts = config_manager.get_all_prompts()
        gatekeeper_base_prompt = prompts.get('gatekeeper_prompt', """
You are a Topical Authority Gatekeeper for a blog. Your goal is to prevent content repetition and ensure new blog posts offer unique value.
You will be given the seed keyword for a new post, a summary/outline of the proposed content (from an analysis step), and a list of titles and snippets from recently published posts on the same blog.

**Your Task:**
1.  Analyze the proposed content summary/outline for the keyword "{keyword}".
2.  Compare the proposed headings, key topics, and overall angle against the list of existing post titles and snippets.
3.  Identify potential issues:
    *   **Duplicate Headings:** Are any proposed headings nearly identical to existing post titles?
    *   **Topic Overlap:** Does the proposed content significantly rehash topics already covered in the existing post snippets?
4.  Provide **actionable feedback** for the Blog Writing AI. Be specific.
    *   If headings are duplicate, suggest concrete alternative headings.
    *   If topics overlap, suggest focusing on a different angle, a more specific sub-topic, or adding new information not present in the existing snippets.
    *   If the proposed content seems sufficiently unique, state that clearly (e.g., "No major overlap detected. Proceed with writing.").
5.  Format your feedback clearly, perhaps using bullet points for specific instructions.

**Proposed Content Summary/Outline:**
---
{proposed_content}
---

**Existing Recent Blog Posts:**
---
{existing_posts}
---

**Gatekeeper Feedback:**
(Provide your feedback here)
""")

        final_prompt = gatekeeper_base_prompt.format(
            keyword=keyword,
            proposed_content=proposed_content_summary,
            existing_posts=existing_posts_formatted
        )

        # 4. Call the Gatekeeper LLM
        print(f"Gatekeeper: Calling LLM ({llm_type} / {model_id or 'default'}) for feedback...")
        gatekeeper_llm = get_llm_instance(llm_type=llm_type, model_id=model_id) # Assumes API key is handled by get_llm_instance/config
        print("Gatekeeper: LLM instance obtained. Generating feedback...")
        feedback = gatekeeper_llm.generate(prompt=final_prompt, max_tokens=500) # Adjust max_tokens as needed
        print(f"Gatekeeper: Feedback received:\n{feedback}")

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Gatekeeper: Error during check. Exception Type: {type(e).__name__}, Message: {e}")
        print(f"Gatekeeper: Full Traceback:\n{error_details}")
        # Try to provide more context in the returned feedback
        feedback = f"Gatekeeper Error: Could not complete check. Type: {type(e).__name__}, Details: {e}"

    print("--- Gatekeeper Check Complete ---")
    return feedback.strip()

def evaluate_blog_post(blog_post_html: str, keyword: str, analysis_results: dict, platform: str, llm_type: str, model_id: str = None) -> dict:
    """
    Evaluates a completed blog post against existing content and quality criteria.
    Checks for topic overlap with existing posts and provides actionable feedback.
    This function evaluates the final blog post AFTER it has been written.

    Args:
        blog_post_html: The HTML content of the blog post to evaluate.
        keyword: The seed keyword for the post.
        analysis_results: The dictionary output from the llm_analyzer step (used for context).
        platform: The target platform ('Shopify' or 'WordPress') to check against.
        llm_type: The LLM provider type for the Gatekeeper.
        model_id: The specific LLM model ID for the Gatekeeper.

    Returns:
        A dictionary containing the evaluation status and feedback:
        {'status': 'APPROVED' | 'REVISION_REQUIRED' | 'ERROR', 'feedback': str}
    """
    print(f"--- Running Gatekeeper Post-Writing Evaluation for '{keyword}' against {platform} ---")
    result = {'status': 'ERROR', 'feedback': 'Evaluation did not complete.'} # Default error state

    try:
        # 1. Fetch existing post summaries (fetch again to get latest)
        print(f"Gatekeeper (Eval): Fetching existing posts from {platform}...")
        existing_posts = fetch_existing_posts(platform)
        if not existing_posts:
            print("Gatekeeper (Eval): No existing posts found or fetched. Approving by default.")
            result['status'] = 'APPROVED'
            result['feedback'] = "No existing posts found to compare against. Approved by default."
            return result

        print(f"Gatekeeper (Eval): Found {len(existing_posts)} existing post summaries to compare against.")

        # 2. Prepare input for Gatekeeper LLM
        # Format existing posts for the prompt
        existing_posts_formatted = "\n".join([f"- Title: {post['title']}\n  Snippet: {post['snippet']}" for post in existing_posts])

        # 3. Construct the prompt
        prompts = config_manager.get_all_prompts()
        # Use a specific prompt for evaluation or adapt the existing one
        gatekeeper_eval_base_prompt = prompts.get('gatekeeper_evaluation_prompt', """
You are a Topical Authority Gatekeeper for a blog. Your goal is to prevent content repetition and ensure new blog posts offer unique value compared to existing content.
You will be given the keyword, the full HTML content of a newly written blog post, and a list of titles and snippets from recently published posts on the same blog.

**Your Task:**
1.  Analyze the **written blog post** for the keyword "{keyword}". Pay attention to its main headings, topics covered, and overall angle.
2.  Compare the written post against the list of **existing post titles and snippets**.
3.  Identify potential issues:
    *   **Significant Topic Overlap:** Does the written post substantially rehash topics already covered in the existing post snippets or titles? Is the angle sufficiently different?
    *   **Redundant Content:** Does the new post add enough unique value compared to what's already published?
4.  Decide on a final status: **APPROVED** or **REVISION_REQUIRED**.
5.  Provide **clear, actionable feedback**, especially if revision is required.
    *   If topics overlap significantly, explain *which* existing posts it overlaps with and suggest specific ways to differentiate the new post (e.g., focus on a different sub-topic, add specific new data/examples, change the angle).
    *   If the post is sufficiently unique, state that clearly.
6.  **Format your response:** Start *immediately* with the status on the first line, followed by your detailed feedback on subsequent lines.
    Example Approved:
    APPROVED
    The post covers the topic from a fresh perspective and doesn't significantly overlap with existing content.

    Example Revision Required:
    REVISION_REQUIRED
    The section on 'Types of Brushes' is very similar to the existing post titled 'Badger vs. Synthetic'. Suggest removing this section or focusing only on a specific niche type not previously covered in detail. The introduction also overlaps heavily with 'How to Clean Your Shaving Brush'. Please rewrite the introduction to focus specifically on '{keyword}'.

**Written Blog Post Content (HTML):**
---
{blog_post_html}
---

**Existing Recent Blog Posts:**
---
{existing_posts}
---

**Gatekeeper Decision and Feedback:**
(Status on first line, feedback below)
""")

        final_prompt = gatekeeper_eval_base_prompt.format(
            keyword=keyword,
            blog_post_html=blog_post_html, # Pass the actual HTML content
            existing_posts=existing_posts_formatted
        )

        # 4. Call the Gatekeeper LLM
        print(f"Gatekeeper (Eval): Calling LLM ({llm_type} / {model_id or 'default'}) for evaluation...")
        gatekeeper_llm = get_llm_instance(llm_type=llm_type, model_id=model_id)
        print("Gatekeeper (Eval): LLM instance obtained. Generating evaluation...")
        llm_response = gatekeeper_llm.generate(prompt=final_prompt, max_tokens=700) # Adjust max_tokens
        print(f"Gatekeeper (Eval): Raw LLM Response:\n{llm_response}")

        # 5. Parse the LLM Response
        response_lines = llm_response.strip().split('\n', 1)
        status_line = response_lines[0].strip().upper()
        feedback_text = response_lines[1].strip() if len(response_lines) > 1 else "No detailed feedback provided by LLM."

        if status_line == 'APPROVED':
            result['status'] = 'APPROVED'
            result['feedback'] = feedback_text
            print("Gatekeeper (Eval): Status set to APPROVED.")
        elif status_line == 'REVISION_REQUIRED' or "REVISION" in status_line: # More robust check
            result['status'] = 'REVISION_REQUIRED'
            result['feedback'] = feedback_text
            print("Gatekeeper (Eval): Status set to REVISION_REQUIRED.")
        else:
            # If LLM response format is unexpected, treat as error but include the response
            result['status'] = 'ERROR'
            result['feedback'] = f"Unexpected response format from Gatekeeper LLM. Raw response:\n{llm_response}"
            print(f"Gatekeeper (Eval): Unexpected status line '{status_line}'. Treating as ERROR.")

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Gatekeeper (Eval): Error during evaluation. Exception Type: {type(e).__name__}, Message: {e}")
        print(f"Gatekeeper (Eval): Full Traceback:\n{error_details}")
        result['status'] = 'ERROR'
        result['feedback'] = f"Gatekeeper Error during evaluation: Could not complete check. Type: {type(e).__name__}, Details: {e}"

    print("--- Gatekeeper Post-Writing Evaluation Complete ---")
    return result

# Example usage (for testing purposes)
if __name__ == '__main__':
    # This block will only run when the script is executed directly
    # Requires config.ini to be set up with platform credentials and API keys
    print("Testing Gatekeeper module...")
    # Mock data (replace with actual calls or test setup)
    mock_keyword = "best shaving brush"
    mock_analysis = {"raw_analysis": "Outline:\n- Introduction to shaving brushes\n- Types of shaving brushes (Badger, Boar, Synthetic)\n- How to choose a brush\n- How to clean a shaving brush\n- Conclusion"}
    mock_platform = "Shopify" # Or "WordPress"
    mock_llm = "openai" # Or your preferred LLM
    
    # Mock blog post HTML for testing the evaluation function
    mock_blog_html = """
    <h1>The Ultimate Guide to Choosing the Best Shaving Brush</h1>
    <p>A quality shaving brush is essential for the perfect shave. This guide will help you choose the right one.</p>
    <h2>Types of Shaving Brushes</h2>
    <p>There are several types of brushes available:</p>
    <ul>
        <li>Badger hair brushes</li>
        <li>Boar bristle brushes</li>
        <li>Synthetic brushes</li>
    </ul>
    <h2>How to Choose the Right Brush</h2>
    <p>Consider factors like bristle type, handle material, and size.</p>
    <h2>Caring for Your Brush</h2>
    <p>Proper maintenance extends the life of your brush.</p>
    <h2>FAQ</h2>
    <h3>How long does a shaving brush last?</h3>
    <p>With proper care, a quality brush can last 5-10 years.</p>
    <h3>Are synthetic brushes as good as natural hair?</h3>
    <p>Modern synthetic brushes perform excellently and are cruelty-free.</p>
    """

    # Add mock functions to posters if they don't exist
    if not hasattr(shopify_poster, 'fetch_recent_posts'):
        def mock_fetch_shopify(url, token, limit):
            print(f"MOCK: Fetching Shopify posts (limit {limit})")
            return [
                {'title': 'How to Clean Your Shaving Brush Properly', 'summary_html': 'Keeping your brush clean is vital...'},
                {'title': 'Badger vs. Synthetic: Which Shaving Brush is Right for You?', 'body_html': 'Debating between badger and synthetic? Let\'s break it down...'}
            ]
        shopify_poster.fetch_recent_posts = mock_fetch_shopify

    if not hasattr(wordpress_poster, 'fetch_recent_posts'):
         def mock_fetch_wp(url, user, pwd, limit):
             print(f"MOCK: Fetching WordPress posts (limit {limit})")
             return [
                 {'title': {'rendered': 'Top 5 Shaving Brushes for Beginners'}, 'excerpt': {'rendered': 'New to wet shaving? Here are our top picks...'}},
                 {'title': {'rendered': 'The Ultimate Guide to Shaving Brush Care'}, 'excerpt': {'rendered': 'Extend the life of your brush with these tips...'}}
             ]
         wordpress_poster.fetch_recent_posts = mock_fetch_wp

    # Ensure config has API key for the mock_llm
    # config_manager.set_setting('API_KEYS', f'{mock_llm}_key', 'YOUR_API_KEY_HERE') # Or load from .env

    try:
        # Test the topic overlap check (pre-writing)
        print("\n--- Testing Topic Overlap Check ---")
        feedback_result = run_gatekeeper_check(
            keyword=mock_keyword,
            analysis_results=mock_analysis,
            platform=mock_platform,
            llm_type=mock_llm
            # model_id="gpt-3.5-turbo" # Optional: specify model
        )
        print("\n--- Gatekeeper Topic Check Result ---")
        print(feedback_result)
        print("-----------------------------")
        
        # Test the blog post evaluation (post-writing)
        print("\n--- Testing Blog Post Evaluation ---")
        evaluation_result_dict = evaluate_blog_post(
            blog_post_html=mock_blog_html,
            keyword=mock_keyword,
            analysis_results=mock_analysis, # Pass analysis for context if needed by prompt
            platform=mock_platform, # Pass platform
            llm_type=mock_llm
            # model_id="gpt-3.5-turbo" # Optional: specify model
        )
        print("\n--- Gatekeeper Evaluation Result ---")
        print(f"Status: {evaluation_result_dict.get('status')}")
        print(f"Feedback: {evaluation_result_dict.get('feedback')}")
        print("-----------------------------")
    except Exception as e:
        print(f"Error during Gatekeeper test run: {e}")
