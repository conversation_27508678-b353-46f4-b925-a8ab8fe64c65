"""
Content Strategist - Main Entry Point

This is the main entry point for the Content Strategist application.
It initializes the database and launches the new GUI interface.
"""

import tkinter as tk
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_window import MainWindow
from database import ContentDatabase

def main():
    """Main entry point for the Content Strategist application."""
    try:
        # Initialize the database
        print("Initializing Content Strategist...")
        db = ContentDatabase()
        print("Database initialized successfully!")

        # Create and run the GUI
        root = tk.Tk()
        app = MainWindow(root)

        print("Content Strategist GUI launched!")
        root.mainloop()

    except ImportError as e:
        print(f"Import error: {e}")
        print("Please make sure all required packages are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting Content Strategist: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
