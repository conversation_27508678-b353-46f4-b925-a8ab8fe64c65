import sqlite3
import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import pickle

# Database file path
DB_FILE = os.path.join(os.path.dirname(__file__), 'content_ledger.db')

class ContentDatabase:
    """
    Database manager for the Content Strategist system.
    Handles all CRUD operations for the content_ledger table.
    """
    
    def __init__(self, db_path: str = None):
        """Initialize the database connection and create tables if needed."""
        self.db_path = db_path or DB_FILE
        self.init_database()
    
    def init_database(self):
        """Create the database and tables if they don't exist."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create the content_ledger table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS content_ledger (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    pillar TEXT NOT NULL,
                    craft TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'NEW',
                    proposed_angle TEXT,
                    freshness_score REAL DEFAULT 0.0,
                    platform_url TEXT,
                    published_date DATETIME,
                    keyword_vector BLOB,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT valid_status CHECK (status IN ('NEW', 'PLANNED', 'WRITING', 'PUBLISHED', 'ON_HOLD', 'REJECTED_USER'))
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON content_ledger(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_freshness_score ON content_ledger(freshness_score)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pillar ON content_ledger(pillar)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_craft ON content_ledger(craft)')
            
            conn.commit()
            print(f"Database initialized at: {self.db_path}")
    
    def insert_content_idea(self, keyword: str, pillar: str, craft: str, 
                           proposed_angle: str = None, keyword_vector: bytes = None) -> int:
        """
        Insert a new content idea into the database.
        
        Args:
            keyword: The target keyword
            pillar: The business pillar this content belongs to
            craft: The craft/category this content belongs to
            proposed_angle: The proposed content angle/approach
            keyword_vector: Serialized vector embedding of the keyword
            
        Returns:
            The ID of the newly inserted record
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO content_ledger 
                (keyword, pillar, craft, proposed_angle, keyword_vector, status)
                VALUES (?, ?, ?, ?, ?, 'NEW')
            ''', (keyword, pillar, craft, proposed_angle, keyword_vector))
            
            content_id = cursor.lastrowid
            conn.commit()
            print(f"Inserted new content idea: ID={content_id}, Keyword='{keyword}', Pillar='{pillar}'")
            return content_id
    
    def get_content_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Retrieve all content records with a specific status.
        
        Args:
            status: The status to filter by ('NEW', 'PLANNED', 'WRITING', etc.)
            
        Returns:
            List of dictionaries representing the records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM content_ledger 
                WHERE status = ? 
                ORDER BY freshness_score DESC, created_date ASC
            ''', (status,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_content_by_statuses(self, statuses: List[str]) -> List[Dict[str, Any]]:
        """
        Retrieve all content records with any of the specified statuses.
        
        Args:
            statuses: List of statuses to filter by
            
        Returns:
            List of dictionaries representing the records
        """
        placeholders = ','.join(['?' for _ in statuses])
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(f'''
                SELECT * FROM content_ledger 
                WHERE status IN ({placeholders})
                ORDER BY freshness_score DESC, created_date ASC
            ''', statuses)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def update_content_status(self, content_id: int, new_status: str) -> bool:
        """
        Update the status of a content record.
        
        Args:
            content_id: The ID of the content record
            new_status: The new status to set
            
        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET status = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_status, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Updated content ID={content_id} status to '{new_status}'")
            else:
                print(f"Failed to update content ID={content_id} - record not found")
            
            return success
    
    def update_freshness_score(self, content_id: int, score: float) -> bool:
        """
        Update the freshness score of a content record.
        
        Args:
            content_id: The ID of the content record
            score: The new freshness score
            
        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET freshness_score = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (score, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Updated content ID={content_id} freshness score to {score}")
            
            return success
    
    def update_content_published(self, content_id: int, platform_url: str) -> bool:
        """
        Mark content as published with the platform URL.
        
        Args:
            content_id: The ID of the content record
            platform_url: The URL where the content was published
            
        Returns:
            True if the update was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE content_ledger 
                SET status = 'PUBLISHED', platform_url = ?, 
                    published_date = CURRENT_TIMESTAMP, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (platform_url, content_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Marked content ID={content_id} as published at {platform_url}")
            
            return success
    
    def get_content_by_id(self, content_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve a specific content record by ID.
        
        Args:
            content_id: The ID of the content record
            
        Returns:
            Dictionary representing the record, or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM content_ledger WHERE id = ?', (content_id,))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_all_content(self) -> List[Dict[str, Any]]:
        """
        Retrieve all content records.
        
        Returns:
            List of dictionaries representing all records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM content_ledger 
                ORDER BY freshness_score DESC, created_date DESC
            ''')
            
            return [dict(row) for row in cursor.fetchall()]
    
    def delete_content(self, content_id: int) -> bool:
        """
        Delete a content record.
        
        Args:
            content_id: The ID of the content record to delete
            
        Returns:
            True if the deletion was successful, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM content_ledger WHERE id = ?', (content_id,))
            
            success = cursor.rowcount > 0
            conn.commit()
            
            if success:
                print(f"Deleted content ID={content_id}")
            else:
                print(f"Failed to delete content ID={content_id} - record not found")
            
            return success
    
    def get_content_stats(self) -> Dict[str, int]:
        """
        Get statistics about content in the database.
        
        Returns:
            Dictionary with counts by status
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT status, COUNT(*) as count 
                FROM content_ledger 
                GROUP BY status
            ''')
            
            stats = {}
            for row in cursor.fetchall():
                stats[row[0]] = row[1]
            
            return stats
    
    def search_content(self, search_term: str) -> List[Dict[str, Any]]:
        """
        Search for content by keyword, pillar, or proposed angle.
        
        Args:
            search_term: The term to search for
            
        Returns:
            List of matching content records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            search_pattern = f'%{search_term}%'
            cursor.execute('''
                SELECT * FROM content_ledger 
                WHERE keyword LIKE ? OR pillar LIKE ? OR proposed_angle LIKE ?
                ORDER BY freshness_score DESC, created_date DESC
            ''', (search_pattern, search_pattern, search_pattern))
            
            return [dict(row) for row in cursor.fetchall()]


# Convenience functions for common operations
def get_database() -> ContentDatabase:
    """Get a database instance."""
    return ContentDatabase()

def serialize_vector(vector) -> bytes:
    """Serialize a vector for storage in the database."""
    return pickle.dumps(vector)

def deserialize_vector(vector_bytes: bytes):
    """Deserialize a vector from the database."""
    return pickle.loads(vector_bytes) if vector_bytes else None


# Example usage and testing
if __name__ == '__main__':
    print("Testing Content Database...")
    
    # Initialize database
    db = ContentDatabase()
    
    # Test inserting content ideas
    test_ideas = [
        ("best shaving brush", "grooming", "shaving", "Comprehensive guide to choosing quality shaving brushes"),
        ("natural beard oil", "grooming", "beard care", "Benefits of natural ingredients in beard care"),
        ("leather wallet care", "leather goods", "accessories", "How to maintain and care for leather wallets"),
        ("sustainable grooming", "sustainability", "eco-friendly", "Eco-friendly alternatives in men's grooming")
    ]
    
    for keyword, pillar, craft, angle in test_ideas:
        db.insert_content_idea(keyword, pillar, craft, angle)
    
    # Test retrieving content
    print("\n--- All Content ---")
    all_content = db.get_all_content()
    for content in all_content:
        print(f"ID: {content['id']}, Keyword: {content['keyword']}, Status: {content['status']}")
    
    # Test status updates
    if all_content:
        first_id = all_content[0]['id']
        db.update_freshness_score(first_id, 85.5)
        db.update_content_status(first_id, 'PLANNED')
    
    # Test statistics
    print("\n--- Content Statistics ---")
    stats = db.get_content_stats()
    for status, count in stats.items():
        print(f"{status}: {count}")
    
    print("\nDatabase testing complete!")
