from anthropic import Anthrop<PERSON>, <PERSON><PERSON>rror, Authentication<PERSON>rror, RateLimitError
from ..llm_interface import LLMInterface
import os
from typing import List

# Default model if not specified
DEFAULT_ANTHROPIC_MODEL = "claude-3-7-sonnet-20250219"

# Hardcoded list of common Anthropic models (as there's no public list models API)
# See: https://docs.anthropic.com/claude/docs/models-overview
KNOWN_ANTHROPIC_MODELS = sorted([
    "claude-3-7-sonnet-20250219",
    "claude-3-5-haiku-20241022", 
    "claude-3-5-sonnet-20241022",
    "claude-3-5-sonnet-20240620",
    "claude-3-opus-20240229",
    "claude-3-sonnet-20240229",
    "claude-3-haiku-20240307",
    "claude-2.1",
    "claude-2.0",
    "claude-instant-1.2",
])

class AnthropicLLM(LLMInterface):
    """Anthropic Claude LLM implementation."""

    def __init__(self, api_key: str = None, model_id: str = None, api_endpoint: str = None):
        # api_endpoint is not typically used for standard Anthropic API
        resolved_api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if not resolved_api_key:
            raise ValueError("Anthropic API key not provided via argument or environment variable.")

        super().__init__(api_key=resolved_api_key, model_id=model_id or DEFAULT_ANTHROPIC_MODEL)
        try:
            self.client = Anthropic(api_key=self.api_key)
            print(f"Anthropic client configured. Default model for generation: {self.model_id}")
        except Exception as e:
            raise ConnectionError(f"Failed to initialize Anthropic client: {e}")

    def generate(self, prompt: str, max_tokens: int = 2048) -> str:
        """Generates text using the Anthropic API."""
        if not self.client:
             raise ConnectionError("Anthropic client not initialized.")

        try:
            # Use the messages API
            response = self.client.messages.create(
                model=self.model_id,
                max_tokens=max_tokens, # Use the passed max_tokens
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
            )

            # Accessing the response content correctly
            if response.content and isinstance(response.content, list) and len(response.content) > 0:
                 # Assuming the first block is the text response
                 if hasattr(response.content[0], 'text'):
                     return response.content[0].text.strip()
                 else:
                     raise ValueError("Anthropic response content block does not contain text.")
            else:
                 raise ValueError("Invalid or empty response structure received from Anthropic.")

        except AuthenticationError as e:
            print(f"Anthropic Authentication error: {e}")
            raise ConnectionError(f"Anthropic Authentication Error: Invalid API Key? Details: {e}") from e
        except RateLimitError as e:
            print(f"Anthropic Rate Limit error: {e}")
            raise ConnectionError(f"Anthropic Rate Limit Exceeded. Please try again later. Details: {e}") from e
        except APIError as e:
            print(f"Anthropic API error: {e}")
            raise Exception(f"Anthropic API error: {e}") from e
        except Exception as e:
            print(f"Error during Anthropic generation: {e}")
            raise Exception(f"Failed to generate text with Anthropic: {e}") from e

    def test_connection(self) -> bool:
        """
        Tests the connection by making a minimal API call.
        Anthropic doesn't have a dedicated 'ping' or 'list models' endpoint,
        so we make a very small generation request.
        """
        if not self.client:
             raise ConnectionError("Anthropic client not initialized.")
        try:
            # Make a very cheap call to test authentication and connectivity
            response = self.client.messages.create(
                model=self.model_id, # Use the configured model
                max_tokens=10,
                messages=[{"role": "user", "content": "Test"}],
            )
            # Check if we got a valid response structure
            if response.content and response.id:
                print("Anthropic connection test successful.")
                return True
            else:
                # This case might indicate an unexpected response format
                raise ConnectionError("Anthropic connection test failed: Received unexpected response format.")

        except AuthenticationError as e:
            print(f"Anthropic connection test failed (Authentication Error): {e}")
            raise ConnectionError("Invalid API key provided. Please check your Anthropic API key.")
        except RateLimitError:
             # If even a test call is rate limited, the key is likely working but account is busy
             print("Anthropic connection test potentially successful (but rate limited).")
             return True # Consider this a success for key validation purposes
        except APIError as e:
            # Handle other API errors (e.g., invalid model, server issues)
            print(f"Anthropic connection test failed (API Error): {e}")
            raise ConnectionError(f"Anthropic API error during connection test: {e}")
        except Exception as e:
            print(f"Anthropic connection test failed (Unexpected Error): {e}")
            raise ConnectionError(f"Unexpected error during Anthropic connection test: {e}")

    def list_models(self) -> List[str]:
        """
        Returns a hardcoded list of known Anthropic models.
        Anthropic API does not currently provide a dynamic model listing endpoint.
        """
        # Key validation should happen in test_connection before this is called.
        print(f"Returning hardcoded list of {len(KNOWN_ANTHROPIC_MODELS)} known Anthropic models.")
        return KNOWN_ANTHROPIC_MODELS

# Example usage (optional, for testing)
if __name__ == '__main__':
    print("Testing Anthropic LLM Implementation...")
    # Assumes ANTHROPIC_API_KEY is set as an environment variable

    try:
        print("\nTesting connection (using env key if set)...")
        anthropic_llm = AnthropicLLM() # Relies on env var
        if anthropic_llm.test_connection():
            print("\nTesting model listing...")
            try:
                models = anthropic_llm.list_models()
                print(f"Available models ({len(models)}):")
                for model in models: # Print all known models
                    print(f"- {model}")
            except Exception as e:
                print(f"Failed to list models: {e}")

            print("\nTesting generation (using default model)...")
            test_prompt = "Explain the concept of AI Alignment in one sentence."
            generated_text = anthropic_llm.generate(test_prompt, max_tokens=50)
            print(f"Prompt: {test_prompt}")
            print(f"Generated Text: {generated_text}")
        else:
             print("Connection test returned False (should have raised exception on failure).")

    except ValueError as e:
         print(f"Skipping test: {e}") # Likely API key not found
    except ConnectionError as e:
         print(f"Connection test failed: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
