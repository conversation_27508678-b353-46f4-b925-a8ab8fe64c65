import requests
import base64
import json

# Helper function to handle API requests
def _make_wp_request(url, method='GET', headers=None, data=None, auth=None, timeout=15):
    """Handles making requests to the WordPress REST API."""
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, auth=auth, timeout=timeout)
        elif method.upper() == 'POST':
            # WordPress REST API expects data in the 'json' parameter for POST
            response = requests.post(url, headers=headers, json=data, auth=auth, timeout=timeout)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        # Check for non-JSON responses before trying to parse
        if 'application/json' not in response.headers.get('Content-Type', ''):
             # Handle potential HTML error pages or non-JSON responses
             if response.status_code >= 400:
                 raise requests.exceptions.HTTPError(f"{response.status_code} Client Error: {response.reason} for url: {response.url}\nResponse: {response.text[:500]}...") # Show beginning of non-JSON error
             else:
                 # Success, but not JSON (unlikely for REST API endpoints we use)
                 return response.text # Or handle as needed

        response_data = response.json()

        # Check for WordPress specific error messages within JSON
        if isinstance(response_data, dict) and response_data.get('code') and 'rest_cannot_create' in response_data.get('code'):
             raise requests.exceptions.HTTPError(f"WordPress Error: {response_data.get('message', 'Cannot create post. Check permissions/data.')}")
        if isinstance(response_data, dict) and response_data.get('code') and 'rest_forbidden' in response_data.get('code'):
             raise requests.exceptions.HTTPError(f"WordPress Error: {response_data.get('message', 'Forbidden. Check authentication/permissions.')}")


        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx) if not caught above
        return response_data

    except requests.exceptions.HTTPError as e:
         # Catch HTTP errors (4xx, 5xx)
         error_message = f"HTTP error: {e}"
         try:
             # Try to get more specific error from response body if JSON
             error_details = response.json()
             if isinstance(error_details, dict) and 'message' in error_details:
                 error_message = f"WordPress API Error: {error_details['message']} (Code: {error_details.get('code', 'N/A')})"
         except json.JSONDecodeError:
             # If response is not JSON, use the original HTTP error text
             error_message = f"HTTP error: {e}\nResponse: {response.text[:500]}..."
         print(error_message)
         raise ConnectionError(error_message) # Raise as ConnectionError for consistency

    except requests.exceptions.RequestException as e:
        print(f"Network error connecting to WordPress: {e}")
        raise ConnectionError(f"Network error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred during WordPress request: {e}")
        raise Exception(f"An unexpected error occurred: {e}")


def test_connection(site_url: str, username: str, app_password: str) -> bool:
    """
    Tests the connection and authentication to the WordPress site using Application Password.
    Uses the REST API's /wp/v2/users/me endpoint.
    """
    if not site_url or not username or not app_password:
        raise ValueError("Site URL, Username, and Application Password are required.")

    api_url = f"{site_url.rstrip('/')}/wp-json/wp/v2/users/me?context=edit" # context=edit usually requires auth
    # Basic Auth using Application Password
    auth = (username, app_password)
    headers = {'User-Agent': 'SEOAssistantPipeline/1.0'} # Good practice to set a user agent

    try:
        response_data = _make_wp_request(api_url, method='GET', headers=headers, auth=auth)
        # If the request succeeds and we get user data with an ID, connection is valid
        if isinstance(response_data, dict) and 'id' in response_data:
            print(f"WordPress connection successful for user: {response_data.get('slug', username)}")
            return True
        else:
            # This case might indicate success but unexpected response format
            print(f"WordPress connection test warning: Authentication likely succeeded but response format was unexpected: {response_data}")
            return False # Treat unexpected format as failure for safety
    except ConnectionError as e:
         # Specific handling for authentication failures if possible
         if "401" in str(e) or "authentication" in str(e).lower() or "forbidden" in str(e).lower():
             raise ConnectionError("Authentication failed. Check Username and Application Password.") from e
         else:
             raise # Re-raise other connection errors

def post_blog_article(site_url: str, username: str, app_password: str, title: str, content_html: str, tags: str = None, published_status: str = 'draft', meta_description: str = None) -> dict:
    """
    Posts a new blog article to a WordPress site using Application Password via REST API.

    Args:
        site_url: The base URL of the WordPress site.
        username: The username for authentication.
        app_password: The Application Password for authentication.
        title: The title of the blog post.
        content_html: The content of the blog post in HTML format.
        tags: Comma-separated string of tags (optional).
        published_status: 'publish', 'draft', 'pending', 'private' (default 'draft').
        meta_description: SEO meta description (optional, attempts common meta keys).

    Returns:
        A dictionary containing the details of the created post, including its ID and URL.

    Raises:
        Exception: If the post creation fails.
    """
    if not site_url or not username or not app_password:
        raise ValueError("Site URL, Username, and Application Password are required.")

    api_url = f"{site_url.rstrip('/')}/wp-json/wp/v2/posts"
    auth = (username, app_password)
    headers = {'User-Agent': 'SEOAssistantPipeline/1.0', 'Content-Type': 'application/json'}

    post_data = {
        'title': title,
        'content': content_html,
        'status': published_status,
    }

    # --- Handle Tags ---
    # This requires knowing the Tag IDs in WordPress. Posting by name is not standard in REST API.
    # For simplicity, we will skip tag creation/assignment via API for now.
    # A more advanced implementation would involve:
    # 1. Fetching existing tags (/wp/v2/tags?search=tagname)
    # 2. Creating tags that don't exist (/wp/v2/tags)
    # 3. Assigning the list of Tag IDs to the 'tags' field in post_data.
    if tags:
        print(f"Note: WordPress tag assignment via API by name is complex and skipped in this version. Tags provided: {tags}")
        # post_data['tags'] = [tag_id_1, tag_id_2] # Example if IDs were known

    # --- Handle Meta Description ---
    # This relies on common SEO plugin meta keys. May not work without a compatible plugin.
    if meta_description:
        post_data['meta'] = {
            # Add keys for popular plugins
            '_yoast_wpseo_metadesc': meta_description, # Yoast
            'rank_math_description': meta_description # Rank Math
            # Add others if known
        }
        print(f"Attempting to set meta description using common SEO plugin keys.")


    try:
        print(f"Posting to WordPress: {title} (Status: {published_status})")
        response_data = _make_wp_request(api_url, method='POST', headers=headers, data=post_data, auth=auth) # Pass data as json

        if response_data and 'id' in response_data:
            print(f"Successfully created WordPress post: ID={response_data['id']}, Title='{response_data.get('title', {}).get('rendered')}'")
            return {
                'id': response_data.get('id'),
                'link': response_data.get('link'),
                'title': response_data.get('title', {}).get('rendered'),
                'status': response_data.get('status')
            }
        else:
            # Should be caught by _make_wp_request error handling, but as fallback:
            raise Exception(f"Failed to create post, unexpected response format: {response_data}")

    except Exception as e:
        print(f"Error posting to WordPress: {e}")
        raise # Re-raise the exception


def fetch_recent_posts(site_url: str, username: str, app_password: str, limit: int = 50) -> list[dict]:
    """
    Fetches recent blog posts from a WordPress site using Application Password via REST API.

    Args:
        site_url: The base URL of the WordPress site.
        username: The username for authentication.
        app_password: The Application Password for authentication.
        limit: The maximum number of posts to fetch (WordPress default is 10, max usually 100).

    Returns:
        A list of dictionaries, each representing a post with keys like
        'id', 'title', 'excerpt', 'link', 'date'.
        Returns an empty list on error.
    """
    if not site_url or not username or not app_password:
        print("Gatekeeper (WP): Site URL, Username, and Application Password are required.")
        return []

    # Adjust limit if necessary (WP REST API max is often 100)
    fetch_limit = min(limit, 100)
    print(f"Fetching up to {fetch_limit} recent posts from WordPress...")

    # Construct the API URL to fetch posts, ordered by date descending
    # Use _fields to request only necessary data
    fields_to_get = "id,date,link,title,excerpt"
    api_url = f"{site_url.rstrip('/')}/wp-json/wp/v2/posts?per_page={fetch_limit}&orderby=date&order=desc&_fields={fields_to_get}&context=view"
    auth = (username, app_password)
    headers = {'User-Agent': 'SEOAssistantPipeline/1.0'}

    posts_data = []
    try:
        response_data = _make_wp_request(api_url, method='GET', headers=headers, auth=auth)

        # Response should be a list of post objects
        if isinstance(response_data, list):
            print(f"Fetched {len(response_data)} posts.")
            # Extract relevant info (adjust keys based on actual WP REST API response)
            for post in response_data:
                posts_data.append({
                    "id": post.get('id'),
                    "title": post.get('title', {}).get('rendered', 'No Title'),
                    "excerpt": post.get('excerpt', {}).get('rendered', ''), # Excerpt is often used for snippets
                    "link": post.get('link'),
                    "date": post.get('date')
                })
        else:
            print(f"WordPress fetch posts warning: Expected a list but received type {type(response_data)}. Response: {response_data}")
            return [] # Return empty list if response format is wrong

    except ConnectionError as e:
         # Handle auth errors specifically if possible
         if "401" in str(e) or "authentication" in str(e).lower() or "forbidden" in str(e).lower():
             print("Gatekeeper (WP): Authentication failed fetching posts. Check Username/App Password.")
         else:
             print(f"Gatekeeper (WP): Connection error fetching posts: {e}")
         return [] # Return empty on connection/auth error
    except Exception as e:
        print(f"Gatekeeper (WP): Unexpected error fetching posts: {e}")
        return [] # Return empty on other errors

    return posts_data


# Example usage (for testing this file directly)
if __name__ == '__main__':
    # --- IMPORTANT: Replace with your actual credentials for testing ---
    # Ensure your WordPress site has REST API enabled and Application Passwords plugin installed/enabled.
    TEST_WP_URL = "YOUR_SITE_URL"          # e.g., https://yourdomain.com
    TEST_WP_USERNAME = "YOUR_WP_USERNAME"
    TEST_WP_APP_PASSWORD = "YOUR_APP_PASSWORD" # Generate via WP Admin > Users > Profile > Application Passwords

    if TEST_WP_URL != "YOUR_SITE_URL" and TEST_WP_USERNAME != "YOUR_WP_USERNAME" and TEST_WP_APP_PASSWORD != "YOUR_APP_PASSWORD":
        print("--- Testing WordPress Connection ---")
        try:
            if test_connection(TEST_WP_URL, TEST_WP_USERNAME, TEST_WP_APP_PASSWORD):
                print("Connection successful!")

                print("\n--- Testing Blog Post Creation (Draft) ---")
                try:
                    import time
                    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                    post_info = post_blog_article(
                        site_url=TEST_WP_URL,
                        username=TEST_WP_USERNAME,
                        app_password=TEST_WP_APP_PASSWORD,
                        title=f"Test Post from SEO Script ({timestamp})",
                        content_html="<h1>Test Heading</h1><p>This is a test blog post generated by the Python script.</p><p>It includes <strong>bold</strong> and <em>italic</em> text.</p>",
                        tags="test, automated script", # Note: Tags might not be assigned unless they exist and IDs are used
                        published_status='draft', # Set to 'publish' to publish directly
                        meta_description=f"Test meta description generated at {timestamp}."
                    )
                    print(f"Post created successfully: {post_info}")
                    print(f"Check your WordPress site drafts!")
                except Exception as post_e:
                    print(f"Error creating post: {post_e}")

            else:
                print("Connection test returned False (unexpected).")
        except Exception as conn_e:
            print(f"Connection test failed: {conn_e}")
    else:
        print("Please update the placeholder credentials (TEST_WP_URL, TEST_WP_USERNAME, TEST_WP_APP_PASSWORD) in the script for testing.")
