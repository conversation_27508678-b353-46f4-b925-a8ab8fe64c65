import requests
import json
from ..llm_interface import LLMInterface
import os
from typing import List, Optional # Added List, Optional

class LocalLLM(LLMInterface):
    """
    Implementation for local LLMs that expose an OpenAI-compatible API,
    such as LMStudio, Ollama, or other local inference servers.
    """

    def __init__(self, api_endpoint: str = None, model_id: str = None, api_key: str = None):
        """
        Initializes the Local LLM interface.
        
        Args:
            api_endpoint: URL for the local LLM server (e.g., "http://localhost:1234/v1")
            model_id: Name of the model (may not be needed for some local servers)
            api_key: Usually not needed for local deployments but included for compatibility
        """
        # Default to localhost if not specified
        resolved_endpoint = api_endpoint or "http://localhost:1234/v1"
        
        # Remove trailing slash if present
        if resolved_endpoint.endswith("/"):
            resolved_endpoint = resolved_endpoint[:-1]
            
        # Make sure the endpoint has the /v1 path
        if not resolved_endpoint.endswith("/v1"):
            if "/v1/" not in resolved_endpoint:
                resolved_endpoint = f"{resolved_endpoint}/v1"
        
        super().__init__(api_key=api_key, model_id=model_id, api_endpoint=resolved_endpoint)
        
        # Test if the endpoint has an OpenAI-compatible interface
        self.completions_endpoint = f"{self.api_endpoint}/chat/completions"
        
    def generate(self, prompt: str, max_tokens: int = 2048) -> str:
        """
        Generates text using the local LLM API.
        
        Args:
            prompt: The input prompt for the LLM
            max_tokens: The maximum length of the generated response
            
        Returns:
            The generated text as a string
            
        Raises:
            Exception: If the generation fails
        """
        if not self.api_endpoint:
            raise ConnectionError("Local LLM API endpoint not initialized.")
            
        try:
            # Format the request in OpenAI-compatible format
            payload = {
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens,
                "temperature": 0.7
            }
            
            # Add model if specified
            if self.model_id:
                payload["model"] = self.model_id
                
            # Add API key if specified (rarely needed for local)
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
                
            # Make the API call
            response = requests.post(
                self.completions_endpoint,
                headers=headers,
                data=json.dumps(payload),
                timeout=120  # Local models might be slower
            )
            
            # Raise an error for non-successful responses
            response.raise_for_status()
            
            # Parse the JSON response
            response_data = response.json()
            
            # Extract the generated text
            if (response_data.get("choices") and 
                len(response_data["choices"]) > 0 and 
                response_data["choices"][0].get("message") and
                response_data["choices"][0]["message"].get("content")):
                
                return response_data["choices"][0]["message"]["content"].strip()
            else:
                raise ValueError(f"Invalid response structure: {response_data}")
                
        except requests.exceptions.RequestException as e:
            print(f"API request error: {e}")
            raise ConnectionError(f"Failed to connect to local LLM API: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e}")
            raise ValueError(f"Failed to parse response as JSON: {e}")
        except Exception as e:
            print(f"Error during local LLM generation: {e}")
            raise Exception(f"Failed to generate text with local LLM: {e}")
            
    def test_connection(self) -> bool:
        """
        Tests the connection to the local LLM API.
        
        Returns:
            True if the connection is successful.
            
        Raises:
            ConnectionError: If the connection test fails.
        """
        try:
            # A simple request to check if the API is working
            # Check specific endpoints for health
            base_url = self.api_endpoint.replace("/v1", "")
            health_endpoints = [f"{base_url}/health", f"{self.api_endpoint}/models"]
            
            for endpoint in health_endpoints:
                try:
                    response = requests.get(endpoint, timeout=5)
                    if response.status_code == 200:
                        print(f"Local LLM server responded at {endpoint}")
                        return True
                except Exception as e:
                    print(f"Endpoint {endpoint} test failed: {e}")
                    continue
            
            # If health checks fail, try a minimal completion as last resort
            minimal_prompt = "Say hello"
            payload = {
                "messages": [{"role": "user", "content": minimal_prompt}],
                "max_tokens": 5
            }
            
            # Add model if specified
            if self.model_id:
                payload["model"] = self.model_id
                
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
                
            response = requests.post(
                self.completions_endpoint,
                headers=headers,
                data=json.dumps(payload),
                timeout=10
            )
            
            if response.status_code < 300:
                print(f"Local LLM API test succeeded with status {response.status_code}")
                return True
            elif response.status_code == 401 or response.status_code == 403:
                raise ConnectionError(f"Authentication failed. Status {response.status_code}")
            elif response.status_code == 404:
                raise ConnectionError(f"API endpoint not found: {self.completions_endpoint}")
            else:
                response_text = ""
                try:
                    response_data = response.json()
                    response_text = str(response_data)
                except:
                    response_text = response.text[:100] if response.text else "No response text"
                raise ConnectionError(f"API returned status {response.status_code}: {response_text}")
                
        except requests.exceptions.ConnectionError:
            raise ConnectionError(f"Could not connect to server at {self.api_endpoint}. Is the server running?")
        except requests.exceptions.Timeout:
            raise ConnectionError(f"Connection to {self.api_endpoint} timed out. Server may be overloaded.")
        except Exception as e:
            print(f"Local LLM connection test failed with unexpected error: {e}")
            raise ConnectionError(f"Failed to connect to local LLM API: {e}")

    def list_models(self) -> List[str]:
        """
        Attempts to fetch a list of available models from the local server's
        OpenAI-compatible /v1/models endpoint. Falls back to defaults if unavailable.
        """
        if not self.api_endpoint:
            raise ConnectionError("Local LLM API endpoint not initialized.")

        models_endpoint = f"{self.api_endpoint}/models"
        try:
            headers = {"Accept": "application/json"}
            if self.api_key: # Include auth if provided
                headers["Authorization"] = f"Bearer {self.api_key}"

            response = requests.get(models_endpoint, headers=headers, timeout=10)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)

            response_data = response.json()

            # Look for OpenAI-like structure: {'data': [{'id': 'model_name'}, ...]}
            if isinstance(response_data, dict) and 'data' in response_data and isinstance(response_data['data'], list):
                model_ids = [model.get('id') for model in response_data['data'] if isinstance(model, dict) and model.get('id')]
                if model_ids:
                    print(f"Fetched {len(model_ids)} models from local endpoint {models_endpoint}.")
                    return sorted(model_ids)

            # Look for simple list structure: ['model1', 'model2'] (less common)
            elif isinstance(response_data, list) and all(isinstance(item, str) for item in response_data):
                 print(f"Fetched {len(response_data)} models (simple list) from local endpoint {models_endpoint}.")
                 return sorted(response_data)

            # Look for Ollama-specific structure: {'models': [{'name': 'model_name:tag'}, ...]}
            elif isinstance(response_data, dict) and 'models' in response_data and isinstance(response_data['models'], list):
                 model_ids = [model.get('name') for model in response_data['models'] if isinstance(model, dict) and model.get('name')]
                 if model_ids:
                     print(f"Fetched {len(model_ids)} models (Ollama style) from local endpoint {models_endpoint}.")
                     return sorted(model_ids)


            print(f"Warning: Could not parse model list from {models_endpoint}. Response: {str(response_data)[:200]}")

        except requests.exceptions.RequestException as e:
            print(f"Warning: Failed to connect or get models from {models_endpoint}: {e}")
        except json.JSONDecodeError as e:
             print(f"Warning: Failed to parse JSON from {models_endpoint}: {e}")
        except Exception as e:
            print(f"Warning: Unexpected error listing local models from {models_endpoint}: {e}")

        # Fallback if API call fails or doesn't return expected format
        print("Falling back to default/configured local model ID.")
        if self.model_id:
            return [self.model_id]
        else:
            # Provide a generic placeholder if no specific model was configured
            return ["local-model-default"]


# Example usage (optional, for testing)
if __name__ == '__main__':
    print("Testing Local LLM Implementation...")
    
    # Example LMStudio endpoint
    lmstudio_endpoint = "http://localhost:1234/v1"
    
    try:
        print(f"\nTesting connection to {lmstudio_endpoint}...")
        local_llm = LocalLLM(api_endpoint=lmstudio_endpoint)
        
        if local_llm.test_connection():
            print("\nTesting model listing...")
            try:
                models = local_llm.list_models()
                print(f"Available models ({len(models)}):")
                for model in models[:10]: # Print first 10
                    print(f"- {model}")
                if len(models) > 10: print("...")
            except Exception as e:
                print(f"Failed to list models: {e}")

            print("\nTesting generation...")
            test_prompt = "Explain the concept of SEO in one sentence."
            generated_text = local_llm.generate(test_prompt, max_tokens=50)
            print(f"Prompt: {test_prompt}")
            print(f"Generated Text: {generated_text}")
        else:
            print("Connection test returned False (should have raised exception on failure).")
            
    except ConnectionError as e:
        print(f"Connection test failed: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
