# Content Strategist - <PERSON><PERSON> Assistant

A proactive, database-driven content planning and execution system that has been completely refactored from the old reactive "SEO Assistant Pipeline" into a powerful "Content Strategist" architecture.

## 🚀 What's New

This application has been completely overhauled with a new architecture:

### Old Architecture (Scrapped)
- ❌ Reactive, loop-based "Gatekeeper" system
- ❌ Business logic scattered in config files
- ❌ Linear checkbox interface
- ❌ Post-writing critique step (inefficient)

### New Architecture (Content Strategist)
- ✅ Proactive, database-driven system
- ✅ All business logic managed in GUI
- ✅ Strategic content planning with freshness scoring
- ✅ Tabbed dashboard interface
- ✅ Linear, expandable pipeline

## 🏗️ Architecture Overview

### Core Components

1. **Database Module (`database.py`)**
   - SQLite database (`content_ledger.db`) for all content management
   - Complete CRUD operations for content ideas
   - Status tracking: NEW → PLANNED → WRITING → PUBLISHED

2. **Idea Generator (`idea_generator.py`)**
   - Combines business pillars with creativity vectors
   - Uses LLMs to generate specific, actionable content ideas
   - Creates vector embeddings for uniqueness scoring
   - Stores all ideas in database with status='NEW'

3. **Planner (`planner.py`)**
   - Analyzes content queue using freshness scoring algorithm
   - Formula: `Final Score = ((TimeScore * 0.5) + (UniquenessScore * 0.5)) * PillarWeight + AngleBonus`
   - Selects highest-scoring content that meets threshold
   - Updates selected content to status='PLANNED'

4. **Worker (`worker.py`)**
   - Executes planned jobs through complete pipeline
   - SERP analysis → Blog writing → Publishing → Database update
   - Uses salvaged core utilities (serp_fetcher, llm_interface, etc.)
   - Updates status to 'PUBLISHED' with platform URL

### Salvaged Core Utilities (Moved to `/core/`)
- `serp_fetcher.py` - SERP API interactions
- `llm_interface.py` + `/llm_implementations/` - Multi-provider LLM factory
- `shopify_poster.py` - Shopify publishing
- `wordpress_poster.py` - WordPress publishing  
- `config_manager.py` - API key management (business logic moved to GUI)

## 🖥️ New GUI Interface

### Tabbed Dashboard
1. **Dashboard Tab**
   - Main control panel with "Run Next Job" button
   - System status display
   - Automated scheduling controls
   - Next job preview

2. **Content Plan Tab**
   - TreeView display of all content in database
   - Management buttons: Prioritize, Veto, Refresh Scores
   - Real-time status and freshness score display

3. **Idea Generator Tab**
   - Interface for generating new content ideas
   - Target craft and creativity vector selection
   - Generated ideas display area

4. **Log Tab**
   - Real-time progress logging
   - Color-coded message levels (info, success, warning, error)
   - Log saving functionality

### Enhanced Settings Window
- **API Keys & Connections** - All LLM and platform credentials
- **LLM Prompts** - Customizable prompts for each pipeline stage
- **Business Logic** (NEW) - Freshness threshold, business pillars, pillar weights

## 📋 Installation & Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Settings
Run the application and go to **File → Settings**:

1. **API Keys & Connections**
   - Add your SERP API key
   - Add LLM provider API keys (OpenAI, Gemini, Anthropic, etc.)
   - Configure Shopify/WordPress credentials

2. **Business Logic**
   - Set freshness threshold (0-100)
   - Define business pillars in format: `Craft Name = Pillar1, Pillar2, Pillar3`
   - Set pillar weights (higher = more priority)

### 3. Test the System
```bash
python test_system.py
```

### 4. Launch the Application
```bash
python main_gui.py
```

## 🎯 Usage Workflow

### 1. Generate Ideas
- Go to **Idea Generator** tab
- Set number of ideas and optional targeting
- Click "Generate New Ideas"
- Ideas are automatically added to database with status='NEW'

### 2. Plan Content
- **Dashboard** shows next job candidate
- **Content Plan** tab shows all content with freshness scores
- Use "Refresh Scores" to recalculate based on current settings
- Use "Prioritize" to manually boost an idea's score

### 3. Execute Jobs
- Click "🚀 Run Next Job" on Dashboard
- System automatically:
  - Selects highest-scoring content above threshold
  - Performs SERP analysis
  - Writes blog content
  - Publishes to configured platform
  - Updates database with published URL

### 4. Monitor Progress
- **Log** tab shows real-time progress
- **Content Plan** tab shows status updates
- **Dashboard** shows queue statistics

## ⚙️ Configuration

### Business Pillars Format
```
Grooming = shaving brushes, beard oil, natural soap, aftershave balm
Leather Goods = wallets, belts, dopp kits, watch straps
Fragrance = cologne, perfume, essential oils
```

### Freshness Scoring
- **Time Score**: Newer ideas score higher (0-100)
- **Uniqueness Score**: More unique ideas score higher (0-100)
- **Pillar Weight**: Multiplier for craft priority (0.1-5.0)
- **Angle Bonus**: Bonus for quality angles (0-20)

### Content Statuses
- **NEW**: Freshly generated ideas
- **PLANNED**: Selected for execution
- **WRITING**: Currently being processed
- **PUBLISHED**: Successfully published with URL
- **ON_HOLD**: Temporarily paused
- **REJECTED_USER**: Manually vetoed

## 🔧 Troubleshooting

### Common Issues
1. **Import Errors**: Run `pip install -r requirements.txt`
2. **No Ideas Generated**: Check LLM API keys and business pillars configuration
3. **No Jobs Selected**: Lower freshness threshold or generate more ideas
4. **Publishing Fails**: Verify platform credentials in settings

### System Test
Run `python test_system.py` to verify all components are working correctly.

## 📁 File Structure
```
├── main_gui.py              # Main entry point
├── test_system.py           # System verification tests
├── database.py              # Database management
├── idea_generator.py        # Content idea generation
├── planner.py              # Strategic content planning
├── worker.py               # Job execution
├── config.ini              # Configuration file
├── content_ledger.db       # SQLite database (created automatically)
├── core/                   # Salvaged utilities
│   ├── serp_fetcher.py
│   ├── llm_interface.py
│   ├── llm_implementations/
│   ├── shopify_poster.py
│   ├── wordpress_poster.py
│   └── config_manager.py
└── gui/                    # User interface
    ├── main_window.py
    └── settings_window.py
```

## 🎉 Success!

The Content Strategist represents a complete transformation from a functional but flawed tool into a powerful, intuitive, and strategic content machine. The clean separation of concerns ensures the database manages state, backend modules handle logic, and the GUI serves as your command center.

Ready to strategically dominate your content creation? Launch the application and start building your content empire! 🚀
