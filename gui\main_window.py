"""
Main Window - The Content Strategist Dashboard

This is the new main interface for the Content Strategist system.
It features a tabbed interface with:
- Dashboard: Main control panel with "Run Next Job" button
- Content Plan: Database view with TreeView and management buttons
- Idea Generator: Interface for generating new content ideas
- Log: Progress logging area
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from gui.settings_window import SettingsWindow
from core import config_manager
from database import ContentDatabase
from idea_generator import IdeaGenerator
from planner import ContentPlanner
from worker import ContentWorker


class MainWindow:
    """
    The main window for the Content Strategist application.
    Features a tabbed interface with dashboard, content planning, idea generation, and logging.
    """
    
    def __init__(self, master):
        self.master = master
        master.title("Content Strategist - SEO Assistant")
        master.geometry("1200x800")
        
        # Initialize components
        self.db = ContentDatabase()
        self.idea_generator = IdeaGenerator()
        self.planner = ContentPlanner()
        self.worker = ContentWorker()
        
        # Initialize variables
        self.is_running = False
        self.current_thread = None
        self.scheduled_job = None
        
        # Create main frame
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the tabbed interface
        self.create_tabbed_interface(main_frame)
        
        # Bind the close event
        master.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Initial data load
        self.refresh_content_plan()
        self.log_message("Content Strategist initialized successfully!")
    
    def create_tabbed_interface(self, parent):
        """Create the main tabbed interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_dashboard_tab()
        self.create_content_plan_tab()
        self.create_idea_generator_tab()
        self.create_log_tab()
        
        # Menu bar
        self.create_menu_bar()
    
    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Settings", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Database Statistics", command=self.show_database_stats)
        tools_menu.add_command(label="Refresh All Scores", command=self.refresh_all_scores)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_dashboard_tab(self):
        """Create the Dashboard tab - main control panel."""
        dashboard_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(dashboard_frame, text="Dashboard")
        
        # Title
        title_label = ttk.Label(dashboard_frame, text="Content Strategist Dashboard", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Status frame
        status_frame = ttk.LabelFrame(dashboard_frame, text="System Status", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.status_text = tk.Text(status_frame, height=4, wrap=tk.WORD, state=tk.DISABLED)
        self.status_text.pack(fill=tk.X)
        
        # Main action frame
        action_frame = ttk.LabelFrame(dashboard_frame, text="Content Operations", padding="20")
        action_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Run Next Job button (large and prominent)
        self.run_job_button = ttk.Button(action_frame, text="🚀 Run Next Job", 
                                        command=self.run_next_job)
        self.run_job_button.pack(pady=10)
        
        # Job info display
        self.next_job_info = tk.Text(action_frame, height=3, wrap=tk.WORD, state=tk.DISABLED)
        self.next_job_info.pack(fill=tk.X, pady=(10, 0))
        
        # Scheduling frame
        schedule_frame = ttk.LabelFrame(dashboard_frame, text="Automated Scheduling", padding="10")
        schedule_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.enable_scheduling_var = tk.BooleanVar()
        ttk.Checkbutton(schedule_frame, text="Enable automated job execution", 
                       variable=self.enable_scheduling_var,
                       command=self.toggle_scheduling).pack(anchor=tk.W)
        
        schedule_controls = ttk.Frame(schedule_frame)
        schedule_controls.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(schedule_controls, text="Run every:").pack(side=tk.LEFT)
        self.schedule_interval_var = tk.IntVar(value=60)
        ttk.Spinbox(schedule_controls, from_=5, to=1440, textvariable=self.schedule_interval_var, 
                   width=8).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(schedule_controls, text="minutes").pack(side=tk.LEFT, padx=(5, 0))
        
        # Update status
        self.update_dashboard_status()
    
    def create_content_plan_tab(self):
        """Create the Content Plan tab - database view and management."""
        content_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(content_frame, text="Content Plan")
        
        # Controls frame
        controls_frame = ttk.Frame(content_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Refresh button
        ttk.Button(controls_frame, text="🔄 Refresh", command=self.refresh_content_plan).pack(side=tk.LEFT, padx=(0, 5))
        
        # Action buttons
        ttk.Button(controls_frame, text="⭐ Prioritize", command=self.prioritize_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="❌ Veto Idea", command=self.veto_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="📊 Refresh Scores", command=self.refresh_all_scores).pack(side=tk.LEFT, padx=5)
        
        # TreeView for content display
        tree_frame = ttk.Frame(content_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create TreeView with scrollbars
        self.content_tree = ttk.Treeview(tree_frame, columns=('Status', 'Freshness', 'Keyword', 'Pillar', 'Angle'), show='tree headings')
        
        # Configure columns
        self.content_tree.heading('#0', text='ID')
        self.content_tree.heading('Status', text='Status')
        self.content_tree.heading('Freshness', text='Freshness')
        self.content_tree.heading('Keyword', text='Keyword')
        self.content_tree.heading('Pillar', text='Pillar')
        self.content_tree.heading('Angle', text='Proposed Angle')
        
        self.content_tree.column('#0', width=50)
        self.content_tree.column('Status', width=80)
        self.content_tree.column('Freshness', width=80)
        self.content_tree.column('Keyword', width=150)
        self.content_tree.column('Pillar', width=120)
        self.content_tree.column('Angle', width=300)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.content_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.content_tree.xview)
        self.content_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack TreeView and scrollbars
        self.content_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Status bar
        self.content_status_var = tk.StringVar(value="Ready")
        ttk.Label(content_frame, textvariable=self.content_status_var).pack(pady=(10, 0))
    
    def create_idea_generator_tab(self):
        """Create the Idea Generator tab."""
        idea_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(idea_frame, text="Idea Generator")
        
        # Controls frame
        controls_frame = ttk.LabelFrame(idea_frame, text="Generation Settings", padding="10")
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Number of ideas
        ttk.Label(controls_frame, text="Number of ideas to generate:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.num_ideas_var = tk.IntVar(value=5)
        ttk.Spinbox(controls_frame, from_=1, to=20, textvariable=self.num_ideas_var, width=8).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Target craft (optional)
        ttk.Label(controls_frame, text="Target craft (optional):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_craft_var = tk.StringVar()
        self.craft_combo = ttk.Combobox(controls_frame, textvariable=self.target_craft_var, state="readonly")
        self.craft_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Target creativity vector (optional)
        ttk.Label(controls_frame, text="Creativity approach (optional):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_vector_var = tk.StringVar()
        self.vector_combo = ttk.Combobox(controls_frame, textvariable=self.target_vector_var, state="readonly")
        self.vector_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Generate button
        ttk.Button(controls_frame, text="💡 Generate New Ideas", command=self.generate_ideas).grid(row=3, column=0, columnspan=2, pady=10)
        
        # Results area
        results_frame = ttk.LabelFrame(idea_frame, text="Generated Ideas", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        self.ideas_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=15)
        self.ideas_text.pack(fill=tk.BOTH, expand=True)
        
        # Load creativity vectors
        self.load_creativity_vectors()
    
    def create_log_tab(self):
        """Create the Log tab."""
        log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(log_frame, text="Log")
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_controls, text="Save Log", command=self.save_log).pack(side=tk.LEFT, padx=(10, 0))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=25)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Configure log text tags for different message types
        self.log_text.tag_configure("info", foreground="black")
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("error", foreground="red")

    def log_message(self, message: str, level: str = "info"):
        """Add a message to the log with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message, level)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

        # Also print to console
        print(formatted_message.strip())

    def update_dashboard_status(self):
        """Update the dashboard status display."""
        try:
            stats = self.db.get_content_stats()
            queue_summary = self.planner.get_queue_summary()

            status_text = f"""Content Queue Status:
• NEW ideas: {stats.get('NEW', 0)}
• ON_HOLD ideas: {stats.get('ON_HOLD', 0)}
• PLANNED ideas: {stats.get('PLANNED', 0)}
• PUBLISHED articles: {stats.get('PUBLISHED', 0)}
• Total in queue: {queue_summary['total_in_queue']}"""

            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete('1.0', tk.END)
            self.status_text.insert('1.0', status_text)
            self.status_text.config(state=tk.DISABLED)

            # Update next job info
            self.update_next_job_info()

        except Exception as e:
            self.log_message(f"Error updating dashboard status: {e}", "error")

    def update_next_job_info(self):
        """Update the next job information display."""
        try:
            # Get business logic settings
            freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            pillar_weights = json.loads(pillar_weights_str)

            # Get top candidate
            scored_content = self.planner.calculate_freshness_scores(pillar_weights, freshness_threshold)

            if scored_content and scored_content[0]['freshness_score'] >= freshness_threshold:
                top_candidate = scored_content[0]
                job_text = f"""Next Job Ready:
Keyword: {top_candidate['keyword']}
Pillar: {top_candidate['pillar']} | Craft: {top_candidate['craft']}
Freshness Score: {top_candidate['freshness_score']:.1f}
Angle: {top_candidate.get('proposed_angle', 'N/A')[:100]}..."""
            else:
                job_text = f"""No job meets threshold ({freshness_threshold:.1f})
Generate more ideas or lower the threshold in Settings > Business Logic"""

            self.next_job_info.config(state=tk.NORMAL)
            self.next_job_info.delete('1.0', tk.END)
            self.next_job_info.insert('1.0', job_text)
            self.next_job_info.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"Error updating next job info: {e}", "error")

    def run_next_job(self):
        """Run the next content job in a separate thread."""
        if self.is_running:
            messagebox.showwarning("Job Running", "A job is already running. Please wait for it to complete.")
            return

        def job_thread():
            try:
                self.is_running = True
                self.run_job_button.config(state=tk.DISABLED, text="Running...")

                self.log_message("Starting next job execution...", "info")

                # Get business logic settings
                freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                pillar_weights = json.loads(pillar_weights_str)

                # Select next job
                job = self.planner.select_next_job(pillar_weights, freshness_threshold)

                if not job:
                    self.log_message("No job selected - threshold not met or no content available", "warning")
                    return

                self.log_message(f"Selected job: '{job['keyword']}' (Score: {job['freshness_score']:.1f})", "info")

                # Execute the job
                result = self.worker.execute_job(job)

                if result['success']:
                    self.log_message(f"Job completed successfully! Published at: {result['platform_url']}", "success")
                else:
                    self.log_message(f"Job failed: {'; '.join(result['errors'])}", "error")

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"Error running job: {e}", "error")
            finally:
                self.is_running = False
                self.master.after(0, lambda: self.run_job_button.config(state=tk.NORMAL, text="🚀 Run Next Job"))

        self.current_thread = threading.Thread(target=job_thread, daemon=True)
        self.current_thread.start()

    def refresh_content_plan(self):
        """Refresh the content plan TreeView."""
        try:
            # Clear existing items
            for item in self.content_tree.get_children():
                self.content_tree.delete(item)

            # Get all content
            all_content = self.db.get_all_content()

            # Populate TreeView
            for content in all_content:
                angle_preview = content.get('proposed_angle', '')[:50] + '...' if content.get('proposed_angle') else 'N/A'

                self.content_tree.insert('', tk.END,
                                       text=str(content['id']),
                                       values=(
                                           content['status'],
                                           f"{content.get('freshness_score', 0):.1f}",
                                           content['keyword'],
                                           content['pillar'],
                                           angle_preview
                                       ))

            self.content_status_var.set(f"Showing {len(all_content)} content ideas")

        except Exception as e:
            self.log_message(f"Error refreshing content plan: {e}", "error")

    def prioritize_selected(self):
        """Set the selected content idea to highest priority."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to prioritize.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])

            # Set freshness score to 999 to ensure it runs next
            self.db.update_freshness_score(content_id, 999.0)

            self.log_message(f"Prioritized content ID {content_id}", "success")
            self.refresh_content_plan()
            self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error prioritizing content: {e}", "error")

    def veto_selected(self):
        """Mark the selected content idea as rejected."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to veto.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])

            # Confirm the action
            if messagebox.askyesno("Confirm Veto", "Are you sure you want to reject this content idea?"):
                self.db.update_content_status(content_id, 'REJECTED_USER')
                self.log_message(f"Vetoed content ID {content_id}", "info")
                self.refresh_content_plan()
                self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error vetoing content: {e}", "error")

    def refresh_all_scores(self):
        """Refresh freshness scores for all content."""
        def refresh_thread():
            try:
                self.log_message("Refreshing all freshness scores...", "info")

                # Get business logic settings
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                pillar_weights = json.loads(pillar_weights_str)

                # Refresh scores
                scored_content = self.planner.calculate_freshness_scores(pillar_weights)

                self.log_message(f"Refreshed scores for {len(scored_content)} content ideas", "success")

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"Error refreshing scores: {e}", "error")

        threading.Thread(target=refresh_thread, daemon=True).start()

    def generate_ideas(self):
        """Generate new content ideas."""
        def generate_thread():
            try:
                self.log_message("Generating new content ideas...", "info")

                # Get settings
                num_ideas = self.num_ideas_var.get()
                target_craft = self.target_craft_var.get() or None
                target_vector = self.target_vector_var.get() or None

                # Get business pillars from settings
                business_pillars = config_manager.get_pipeline_setting('business_pillars')
                if not business_pillars:
                    self.log_message("No business pillars configured. Please set them in Settings > Business Logic", "error")
                    return

                # Generate ideas
                ideas = self.idea_generator.generate_ideas(
                    pillars_text=business_pillars,
                    target_craft=target_craft,
                    target_creativity_vector=target_vector,
                    num_ideas=num_ideas
                )

                # Display results
                results_text = f"Generated {len(ideas)} new content ideas:\n\n"
                for i, idea in enumerate(ideas, 1):
                    results_text += f"{i}. {idea['keyword']}\n"
                    results_text += f"   Pillar: {idea['pillar']} | Craft: {idea['craft']}\n"
                    results_text += f"   Angle: {idea['proposed_angle']}\n\n"

                self.master.after(0, lambda: self.display_generated_ideas(results_text))
                self.log_message(f"Successfully generated {len(ideas)} new ideas", "success")

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"Error generating ideas: {e}", "error")

        threading.Thread(target=generate_thread, daemon=True).start()

    def display_generated_ideas(self, text: str):
        """Display generated ideas in the text area."""
        self.ideas_text.delete('1.0', tk.END)
        self.ideas_text.insert('1.0', text)

    def load_creativity_vectors(self):
        """Load creativity vectors and crafts into the comboboxes."""
        try:
            # Load creativity vectors
            vectors = self.idea_generator.get_creativity_vectors()
            self.vector_combo['values'] = [''] + vectors

            # Load crafts from business pillars
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
            crafts = []
            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft = line.split('=', 1)[0].strip()
                    if craft:
                        crafts.append(craft)

            self.craft_combo['values'] = [''] + crafts

        except Exception as e:
            self.log_message(f"Error loading creativity vectors: {e}", "error")

    def toggle_scheduling(self):
        """Toggle automated scheduling on/off."""
        if self.enable_scheduling_var.get():
            self.start_scheduling()
        else:
            self.stop_scheduling()

    def start_scheduling(self):
        """Start automated job scheduling."""
        interval_minutes = self.schedule_interval_var.get()
        interval_ms = interval_minutes * 60 * 1000  # Convert to milliseconds

        def scheduled_run():
            if self.enable_scheduling_var.get() and not self.is_running:
                self.run_next_job()

            # Schedule next run
            if self.enable_scheduling_var.get():
                self.scheduled_job = self.master.after(interval_ms, scheduled_run)

        self.log_message(f"Started automated scheduling (every {interval_minutes} minutes)", "info")
        self.scheduled_job = self.master.after(interval_ms, scheduled_run)

    def stop_scheduling(self):
        """Stop automated job scheduling."""
        if self.scheduled_job:
            self.master.after_cancel(self.scheduled_job)
            self.scheduled_job = None
        self.log_message("Stopped automated scheduling", "info")

    def open_settings(self):
        """Open the settings window."""
        settings_window = tk.Toplevel(self.master)
        SettingsWindow(settings_window)

        # Refresh data after settings window closes
        settings_window.wait_window()
        self.load_creativity_vectors()
        self.update_dashboard_status()

    def show_database_stats(self):
        """Show database statistics."""
        try:
            stats = self.db.get_content_stats()
            total = sum(stats.values())

            stats_text = "Database Statistics:\n\n"
            for status, count in stats.items():
                percentage = (count / total * 100) if total > 0 else 0
                stats_text += f"{status}: {count} ({percentage:.1f}%)\n"

            stats_text += f"\nTotal Content Ideas: {total}"

            messagebox.showinfo("Database Statistics", stats_text)

        except Exception as e:
            self.log_message(f"Error getting database stats: {e}", "error")

    def show_about(self):
        """Show about dialog."""
        about_text = """Content Strategist - SEO Assistant

A proactive, database-driven content planning and execution system.

Features:
• Automated content idea generation
• Strategic content planning with freshness scoring
• Automated SERP analysis and blog writing
• Direct publishing to Shopify and WordPress
• Complete content lifecycle management

Built with Python and powered by advanced LLM integration."""

        messagebox.showinfo("About Content Strategist", about_text)

    def clear_log(self):
        """Clear the log text area."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete('1.0', tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_log(self):
        """Save the log to a file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Save Log File"
            )

            if filename:
                log_content = self.log_text.get('1.0', tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log_message(f"Log saved to {filename}", "success")

        except Exception as e:
            self.log_message(f"Error saving log: {e}", "error")

    def on_closing(self):
        """Handle window closing."""
        if self.is_running:
            if messagebox.askokcancel("Job Running", "A job is currently running. Do you want to exit anyway?"):
                self.stop_scheduling()
                self.master.destroy()
        else:
            self.stop_scheduling()
            self.master.destroy()
