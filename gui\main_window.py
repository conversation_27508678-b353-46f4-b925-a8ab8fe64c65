import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import re # For keyword analysis
from gui.settings_window import SettingsWindow # Changed from relative import
import json # Added for LLM response parsing
from core import config_manager
from core.config_manager import load_config # Import load_config specifically
# Import the registry and factory function
from core.llm_interface import AVAILABLE_MODELS, get_llm_instance # Added get_llm_instance
# Import core modules for pipeline execution
from core import serp_fetcher, llm_analyzer, blog_writer, blog_critic, shopify_poster, wordpress_poster, gatekeeper # Added gatekeeper

class MainWindow:
    def __init__(self, master):
        self.master = master
        master.title("SEO Assistant Pipeline")
        master.geometry("1100x1200") # Increased window size further for visibility

        # Initialize variables that will be needed in load_pipeline_settings
        self.serp_limit_var = tk.IntVar(value=1)  # Default to 1 call for safety
        # Scheduling variables
        self.enable_scheduling_var = tk.BooleanVar(value=False)  # For scheduled runs
        self.run_count_var = tk.IntVar(value=1)  # Number of times to run
        self.run_interval_var = tk.IntVar(value=20)  # Interval between runs
        self.interval_unit_var = tk.StringVar(value="minutes")  # Unit for interval (seconds/minutes)
        self.scheduled_run_active = False  # Flag to track if scheduled run is active
        self.current_run_count = 0  # Counter for scheduled runs
        self.schedule_timer = None  # To store the timer reference
        # self.enable_serp_var = tk.BooleanVar(value=True) # Replaced by serp_source_var
        self.serp_source_var = tk.StringVar(value="Use SERP API (Recommended)") # New dropdown variable
        self.enable_analysis_var = tk.BooleanVar(value=True)
        self.enable_gatekeeper_var = tk.BooleanVar(value=False) # Gatekeeper Variable Added
        self.enable_writing_var = tk.BooleanVar(value=True)
        self.enable_review_var = tk.BooleanVar(value=True)
        self.enable_posting_var = tk.BooleanVar(value=True) # Checkbox to enable/disable posting step
        self.posting_platform_var = tk.StringVar() # Dropdown for selecting platform (populated dynamically)
        self.analysis_llm_var = tk.StringVar(value="local") # Provider selection
        self.gatekeeper_llm_var = tk.StringVar(value="openai") # Gatekeeper Provider Added
        self.writing_llm_var = tk.StringVar(value="openai") # Provider selection
        self.critic_llm_var = tk.StringVar(value="openai") # Provider selection
        self.analysis_model_var = tk.StringVar() # Specific model selection
        self.gatekeeper_model_var = tk.StringVar() # Gatekeeper Model Added
        self.writing_model_var = tk.StringVar() # Specific model selection
        self.critic_model_var = tk.StringVar() # Specific model selection
        self.word_count_var = tk.IntVar(value=1000)
        self.tone_var = tk.StringVar(value="Professional")
        self.meta_description_var = tk.StringVar() # For SEO Meta tab
        self.upload_meta_description_var = tk.BooleanVar(value=False) # Checkbox for uploading meta desc
        self.publish_immediately_var = tk.BooleanVar(value=False)
        self.additional_tags_var = tk.StringVar()
        # Batch keywords
        self.batch_keywords = []
        self.current_batch_index = 0
        # Countdown timer
        self.countdown_var = tk.StringVar()
        self.countdown_timer_id = None # To store the 'after' ID for the countdown timer

        # SERP options
        self.serp_type_var = tk.StringVar(value="Google Search")
        self.country_code_var = tk.StringVar(value="us")
        self.language_code_var = tk.StringVar(value="en")
        
        # --- Load Initial Config ---
        self.load_pipeline_settings()

        # --- Main Frame ---
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # --- Keyword Input ---
        keyword_frame = ttk.LabelFrame(main_frame, text="Seed Keyword", padding="10")
        keyword_frame.pack(fill=tk.X, pady=5)

        self.keyword_var = tk.StringVar()
        self.keyword_entry = ttk.Entry(keyword_frame, textvariable=self.keyword_var, width=60) # Made instance variable
        self.keyword_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.batch_button = ttk.Button(keyword_frame, text="Batch Keywords", command=self.open_batch_keyword_dialog)
        self.batch_button.pack(side=tk.LEFT, padx=5)

        # --- Action Buttons at Top ---
        top_button_frame = ttk.Frame(main_frame, padding="10")
        top_button_frame.pack(fill=tk.X, pady=5)

        # Create a style for the Run Pipeline button to make it stand out
        style = ttk.Style()
        style.configure("Run.TButton", font=("Arial", 11, "bold"), background="#4CAF50")
        
        self.run_button = ttk.Button(top_button_frame, text="Run Pipeline", command=self.run_pipeline, style="Run.TButton")
        self.run_button.pack(side=tk.LEFT, padx=5, ipadx=10, ipady=5)  # Make button larger with padding

        self.settings_button = ttk.Button(top_button_frame, text="Settings", command=self.open_settings)
        self.settings_button.pack(side=tk.RIGHT, padx=5)

        # --- Pipeline Options ---
        options_frame = ttk.LabelFrame(main_frame, text="Pipeline Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)
        
        # SERP API Call Limit
        serp_limit_frame = ttk.Frame(options_frame)
        serp_limit_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(serp_limit_frame, text="Max API Calls:").pack(side=tk.LEFT, padx=5)
        # serp_limit_var is already initialized in __init__
        ttk.Spinbox(serp_limit_frame, from_=0, to=100, textvariable=self.serp_limit_var, width=5).pack(side=tk.LEFT, padx=5)
        ttk.Label(serp_limit_frame, text="(Set to 1 to minimize API usage; 0 for no limit)").pack(side=tk.LEFT, padx=5)
        
        # Scheduling options
        schedule_frame = ttk.Frame(options_frame)
        schedule_frame.pack(fill=tk.X, pady=2)

        # Add trace to update run button text when scheduling is enabled/disabled
        self.enable_scheduling_var.trace_add("write", lambda *args: self.update_run_button_text())
        ttk.Checkbutton(schedule_frame, text="Enable Scheduled Runs", variable=self.enable_scheduling_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(schedule_frame, text="Run Count:").pack(side=tk.LEFT, padx=(20, 5))
        run_count_spinbox = ttk.Spinbox(schedule_frame, from_=0, to=100, textvariable=self.run_count_var, width=5)
        run_count_spinbox.pack(side=tk.LEFT, padx=5)
        ttk.Label(schedule_frame, text="(0 for indefinite)").pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(schedule_frame, text="Interval:").pack(side=tk.LEFT, padx=5)
        ttk.Spinbox(schedule_frame, from_=1, to=1440, textvariable=self.run_interval_var, width=5).pack(side=tk.LEFT, padx=5)
        
        interval_unit_combo = ttk.Combobox(schedule_frame, textvariable=self.interval_unit_var, values=["seconds", "minutes"], state="readonly", width=10)
        interval_unit_combo.pack(side=tk.LEFT, padx=5)

        # --- Pipeline Steps (Gating) ---
        pipeline_frame = ttk.LabelFrame(main_frame, text="Pipeline Steps", padding="10")
        pipeline_frame.pack(fill=tk.X, pady=5)

        # --- Horizontal Grid Layout for Pipeline Steps ---
        col_index = 0
        # Step 1: Keyword Source (SERP API or LLM)
        step1_frame = ttk.Frame(pipeline_frame)
        step1_frame.grid(row=0, column=col_index, padx=5, pady=5, sticky=tk.W)
        ttk.Label(step1_frame, text="1. Keywords/PAA:").pack(side=tk.LEFT)
        self.serp_source_combo = ttk.Combobox(step1_frame, textvariable=self.serp_source_var,
                                              values=["Use SERP API (Recommended)", "Use Analysis LLM (Fallback)"],
                                              state="readonly", width=28)
        self.serp_source_combo.pack(side=tk.LEFT, padx=5)
        col_index += 1
        ttk.Label(pipeline_frame, text="->").grid(row=0, column=col_index, padx=2, pady=5)
        col_index += 1

        # Step 2: Analyze Data (SERP or LLM Output)
        # Note: The label text might need adjustment depending on whether SERP or LLM was used in step 1
        self.enable_analysis_cb = ttk.Checkbutton(pipeline_frame, text="2. Analyze Data", variable=self.enable_analysis_var)
        self.enable_analysis_cb.grid(row=0, column=col_index, padx=5, pady=5, sticky=tk.W)
        col_index += 1
        ttk.Label(pipeline_frame, text="->").grid(row=0, column=col_index, padx=2, pady=5)
        col_index += 1

        # Step 2.5: Gatekeeper
        self.enable_gatekeeper_cb = ttk.Checkbutton(pipeline_frame, text="2.5 Gatekeeper", variable=self.enable_gatekeeper_var)
        self.enable_gatekeeper_cb.grid(row=0, column=col_index, padx=5, pady=5, sticky=tk.W)
        col_index += 1
        ttk.Label(pipeline_frame, text="->").grid(row=0, column=col_index, padx=2, pady=5)
        col_index += 1

        # Step 3: Write Blog
        self.enable_writing_cb = ttk.Checkbutton(pipeline_frame, text="3. Write Blog", variable=self.enable_writing_var)
        self.enable_writing_cb.grid(row=0, column=col_index, padx=5, pady=5, sticky=tk.W)
        col_index += 1
        ttk.Label(pipeline_frame, text="->").grid(row=0, column=col_index, padx=2, pady=5)
        col_index += 1

        # Step 4: Review Blog
        self.enable_review_cb = ttk.Checkbutton(pipeline_frame, text="4. Review Blog", variable=self.enable_review_var)
        self.enable_review_cb.grid(row=0, column=col_index, padx=5, pady=5, sticky=tk.W)
        col_index += 1
        ttk.Label(pipeline_frame, text="->").grid(row=0, column=col_index, padx=2, pady=5)
        col_index += 1

        # Step 5: Post Blog
        post_frame = ttk.Frame(pipeline_frame) # Keep post elements together
        post_frame.grid(row=0, column=col_index, padx=5, pady=5, sticky=tk.W)
        self.enable_posting_cb = ttk.Checkbutton(post_frame, text="5. Post to:", variable=self.enable_posting_var)
        self.enable_posting_cb.pack(side=tk.LEFT)
        self.posting_platform_selector = ttk.Combobox(post_frame, textvariable=self.posting_platform_var, state="disabled", width=15) # Start disabled
        self.posting_platform_selector.pack(side=tk.LEFT, padx=5)
        # col_index += 1 # Increment if adding more steps after posting

        # Configure columns to have some weight if needed for resizing, though likely fixed width is fine here
        # for i in range(col_index):
        #     pipeline_frame.columnconfigure(i, weight=1)


        # --- LLM Selection ---
        llm_frame = ttk.LabelFrame(main_frame, text="LLM Selection", padding="10")
        llm_frame.pack(fill=tk.X, pady=5)

        # List of available LLM providers
        # This list should ideally match the keys in AVAILABLE_MODELS from llm_interface
        llm_provider_options = ["openai", "gemini", "anthropic", "openrouter", "groq", "local"] # Added openrouter, groq

        # --- Analysis LLM ---
        ttk.Label(llm_frame, text="Analysis Provider:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        # analysis_llm_var is already initialized in __init__
        self.analysis_provider_combo = ttk.Combobox(llm_frame, textvariable=self.analysis_llm_var, values=llm_provider_options, state="readonly", width=15)
        self.analysis_provider_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)

        ttk.Label(llm_frame, text="Analysis Model:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        # analysis_model_var is already initialized in __init__
        self.analysis_model_combo = ttk.Combobox(llm_frame, textvariable=self.analysis_model_var, state="disabled", width=30)
        self.analysis_model_combo.grid(row=0, column=3, padx=5, pady=5, sticky=tk.EW)

        # --- Gatekeeper LLM --- Added
        ttk.Label(llm_frame, text="Gatekeeper Provider:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.gatekeeper_provider_combo = ttk.Combobox(llm_frame, textvariable=self.gatekeeper_llm_var, values=llm_provider_options, state="readonly", width=15)
        self.gatekeeper_provider_combo.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)

        ttk.Label(llm_frame, text="Gatekeeper Model:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.gatekeeper_model_combo = ttk.Combobox(llm_frame, textvariable=self.gatekeeper_model_var, state="disabled", width=30)
        self.gatekeeper_model_combo.grid(row=1, column=3, padx=5, pady=5, sticky=tk.EW)

        # --- Writing LLM ---
        ttk.Label(llm_frame, text="Writing Provider:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.writing_provider_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_var, values=llm_provider_options, state="readonly", width=15)
        self.writing_provider_combo.grid(row=2, column=1, padx=5, pady=5, sticky=tk.EW)

        ttk.Label(llm_frame, text="Writing Model:").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        self.writing_model_combo = ttk.Combobox(llm_frame, textvariable=self.writing_model_var, state="disabled", width=30)
        self.writing_model_combo.grid(row=2, column=3, padx=5, pady=5, sticky=tk.EW)
        
        # --- Critic LLM ---
        ttk.Label(llm_frame, text="Critic Provider:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.critic_provider_combo = ttk.Combobox(llm_frame, textvariable=self.critic_llm_var, values=llm_provider_options, state="readonly", width=15)
        self.critic_provider_combo.grid(row=3, column=1, padx=5, pady=5, sticky=tk.EW)

        ttk.Label(llm_frame, text="Critic Model:").grid(row=3, column=2, padx=5, pady=5, sticky=tk.W)
        self.critic_model_combo = ttk.Combobox(llm_frame, textvariable=self.critic_model_var, state="disabled", width=30)
        self.critic_model_combo.grid(row=3, column=3, padx=5, pady=5, sticky=tk.EW)

        # Configure column weights for expansion
        llm_frame.columnconfigure(1, weight=1)
        llm_frame.columnconfigure(3, weight=2)


        # Note: Action buttons are now at the top of the window

        # --- Advanced Options Frame ---
        options_frame = ttk.LabelFrame(main_frame, text="Advanced Options", padding="10")
        options_frame.pack(fill=tk.X, pady=5)
        
        # Create notebook for advanced options
        options_notebook = ttk.Notebook(options_frame)
        options_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Blog Writing Options tab
        blog_options_frame = ttk.Frame(options_notebook, padding="5")
        options_notebook.add(blog_options_frame, text="Blog Writing")
        
        ttk.Label(blog_options_frame, text="Word Count:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        # word_count_var is already initialized in __init__
        ttk.Spinbox(blog_options_frame, from_=500, to=5000, increment=100, textvariable=self.word_count_var, width=7).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(blog_options_frame, text="Tone:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        # tone_var is already initialized in __init__
        ttk.Combobox(blog_options_frame, textvariable=self.tone_var, values=["Professional", "Conversational", "Educational", "Humorous"], width=15).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)

        # SEO Meta tab (New)
        seo_meta_frame = ttk.Frame(options_notebook, padding="5")
        options_notebook.add(seo_meta_frame, text="SEO Meta")

        ttk.Label(seo_meta_frame, text="Meta Description:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        # meta_description_var is initialized in __init__
        meta_entry = ttk.Entry(seo_meta_frame, textvariable=self.meta_description_var, width=60)
        meta_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Label(seo_meta_frame, text="(~150-160 chars)").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        # Add checkbox for uploading meta description
        ttk.Checkbutton(seo_meta_frame, text="Upload Meta Description to Shopify", variable=self.upload_meta_description_var).grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky=tk.W)
        seo_meta_frame.columnconfigure(1, weight=1) # Allow entry to expand


        # Shopify Options tab
        shopify_options_frame = ttk.Frame(options_notebook, padding="5")
        options_notebook.add(shopify_options_frame, text="Shopify")
        
        # publish_immediately_var is already initialized in __init__
        ttk.Checkbutton(shopify_options_frame, text="Publish immediately (otherwise save as draft)", variable=self.publish_immediately_var).grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(shopify_options_frame, text="Additional Tags:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        # additional_tags_var is already initialized in __init__
        ttk.Entry(shopify_options_frame, textvariable=self.additional_tags_var, width=30).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Label(shopify_options_frame, text="(comma separated)").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)

        # SERP Type Options
        serp_options_frame = ttk.Frame(options_notebook, padding="5")
        options_notebook.add(serp_options_frame, text="SERP Options")
        
        # SERP API Type
        ttk.Label(serp_options_frame, text="SERP API Type:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        # serp_type_var is already initialized in __init__
        serp_type_combo = ttk.Combobox(serp_options_frame, textvariable=self.serp_type_var, 
                                     values=["Google Search", "Google Autocomplete", "Combined (Search + Autocomplete)"], 
                                     width=30, state="readonly")
        serp_type_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Country code
        ttk.Label(serp_options_frame, text="Country Code:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        # country_code_var is already initialized in __init__
        country_entry = ttk.Entry(serp_options_frame, textvariable=self.country_code_var, width=5)
        country_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Label(serp_options_frame, text="(e.g., us, uk, au)").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        
        # Language code
        ttk.Label(serp_options_frame, text="Language Code:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        # language_code_var is already initialized in __init__
        language_entry = ttk.Entry(serp_options_frame, textvariable=self.language_code_var, width=5)
        language_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Label(serp_options_frame, text="(e.g., en, es, fr)").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)

        # --- Output/Status Area with Tabs ---
        output_frame = ttk.LabelFrame(main_frame, text="Output / Status", padding="10")
        output_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Create notebook for output tabs
        self.output_notebook = ttk.Notebook(output_frame)
        self.output_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Status bar at the bottom of the main window
        status_frame = ttk.Frame(master)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_var = tk.StringVar(value="Ready")
        self.status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True) # Pack left and expand

        # Add countdown label to the status frame
        self.countdown_label = ttk.Label(status_frame, textvariable=self.countdown_var, relief=tk.SUNKEN, anchor=tk.E)
        self.countdown_label.pack(side=tk.RIGHT, padx=5) # Pack right

        # Log tab
        log_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(log_frame, text="Log")
        
        self.output_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15, state=tk.DISABLED)
        self.output_text.pack(fill=tk.BOTH, expand=True)

        # Keyword Ideas tab (New)
        keyword_ideas_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(keyword_ideas_frame, text="Keyword Ideas")
        self.keyword_ideas_text = scrolledtext.ScrolledText(keyword_ideas_frame, wrap=tk.WORD, height=15)
        self.keyword_ideas_text.pack(fill=tk.BOTH, expand=True)
        # Add buttons for Keyword Ideas tab
        keyword_ideas_button_frame = ttk.Frame(keyword_ideas_frame)
        keyword_ideas_button_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(keyword_ideas_button_frame, text="Copy to Clipboard",
                   command=lambda: self.copy_to_clipboard(self.keyword_ideas_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(keyword_ideas_button_frame, text="Save to File",
                   command=lambda: self.save_to_file(self.keyword_ideas_text, "keyword_ideas.txt")).pack(side=tk.LEFT, padx=5)

        # SERP Data tab
        serp_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(serp_frame, text="SERP Data")
        
        # Add a frame for the SERP data text area and buttons
        serp_content_frame = ttk.Frame(serp_frame)
        serp_content_frame.pack(fill=tk.BOTH, expand=True)
        
        self.serp_data_text = scrolledtext.ScrolledText(serp_content_frame, wrap=tk.WORD, height=15)
        self.serp_data_text.pack(fill=tk.BOTH, expand=True)
        
        # Add button frame for SERP data
        serp_button_frame = ttk.Frame(serp_frame)
        serp_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(serp_button_frame, text="Copy to Clipboard", 
                  command=lambda: self.copy_to_clipboard(self.serp_data_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(serp_button_frame, text="Save to File", 
                  command=lambda: self.save_to_file(self.serp_data_text, "serp_data.json")).pack(side=tk.LEFT, padx=5)
        
        # Analysis tab
        analysis_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(analysis_frame, text="Analysis")
        
        # Add a frame for the Analysis text area and buttons
        analysis_content_frame = ttk.Frame(analysis_frame)
        analysis_content_frame.pack(fill=tk.BOTH, expand=True)
        
        self.analysis_text = scrolledtext.ScrolledText(analysis_content_frame, wrap=tk.WORD, height=15)
        self.analysis_text.pack(fill=tk.BOTH, expand=True)
        
        # Add button frame for Analysis
        analysis_button_frame = ttk.Frame(analysis_frame)
        analysis_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(analysis_button_frame, text="Copy to Clipboard", 
                  command=lambda: self.copy_to_clipboard(self.analysis_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(analysis_button_frame, text="Save to File", 
                  command=lambda: self.save_to_file(self.analysis_text, "analysis.txt")).pack(side=tk.LEFT, padx=5)
        
        # Blog Content tab
        blog_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(blog_frame, text="Blog Content")
        
        # Review tab
        review_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(review_frame, text="Review")
        
        # Add a frame for the Review text area and buttons
        review_content_frame = ttk.Frame(review_frame)
        review_content_frame.pack(fill=tk.BOTH, expand=True)
        
        self.review_text = scrolledtext.ScrolledText(review_content_frame, wrap=tk.WORD, height=15)
        self.review_text.pack(fill=tk.BOTH, expand=True)
        
        # Add button frame for Review content
        review_button_frame = ttk.Frame(review_frame)
        review_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(review_button_frame, text="Copy to Clipboard", 
                  command=lambda: self.copy_to_clipboard(self.review_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(review_button_frame, text="Save to File", 
                  command=lambda: self.save_to_file(self.review_text, "blog_review.txt")).pack(side=tk.LEFT, padx=5)
        
        # Add a frame for the Blog content text area and buttons
        blog_content_frame = ttk.Frame(blog_frame)
        blog_content_frame.pack(fill=tk.BOTH, expand=True)
        
        self.blog_text = scrolledtext.ScrolledText(blog_content_frame, wrap=tk.WORD, height=15)
        self.blog_text.pack(fill=tk.BOTH, expand=True)
        
        # Add button frame for Blog content
        blog_button_frame = ttk.Frame(blog_frame)
        blog_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(blog_button_frame, text="Copy to Clipboard", 
                  command=lambda: self.copy_to_clipboard(self.blog_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(blog_button_frame, text="Save to File", 
                  command=lambda: self.save_to_file(self.blog_text, "blog_content.html")).pack(side=tk.LEFT, padx=5)

        # Keyword Analysis tab (New)
        keyword_analysis_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(keyword_analysis_frame, text="Keyword Analysis")
        self.keyword_analysis_text = scrolledtext.ScrolledText(keyword_analysis_frame, wrap=tk.WORD, height=15)
        self.keyword_analysis_text.pack(fill=tk.BOTH, expand=True)
        # Add buttons for Keyword Analysis tab
        keyword_analysis_button_frame = ttk.Frame(keyword_analysis_frame)
        keyword_analysis_button_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(keyword_analysis_button_frame, text="Copy to Clipboard",
                   command=lambda: self.copy_to_clipboard(self.keyword_analysis_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(keyword_analysis_button_frame, text="Save to File",
                   command=lambda: self.save_to_file(self.keyword_analysis_text, "keyword_analysis.txt")).pack(side=tk.LEFT, padx=5)

        # Gatekeeper Feedback tab (New)
        gatekeeper_frame = ttk.Frame(self.output_notebook, padding="5")
        self.output_notebook.add(gatekeeper_frame, text="Gatekeeper Feedback")
        self.gatekeeper_feedback_text = scrolledtext.ScrolledText(gatekeeper_frame, wrap=tk.WORD, height=15)
        self.gatekeeper_feedback_text.pack(fill=tk.BOTH, expand=True)
        # Add buttons for Gatekeeper Feedback tab
        gatekeeper_button_frame = ttk.Frame(gatekeeper_frame)
        gatekeeper_button_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(gatekeeper_button_frame, text="Copy to Clipboard",
                   command=lambda: self.copy_to_clipboard(self.gatekeeper_feedback_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(gatekeeper_button_frame, text="Save to File",
                   command=lambda: self.save_to_file(self.gatekeeper_feedback_text, "gatekeeper_feedback.txt")).pack(side=tk.LEFT, padx=5)


        # --- Add Traces for Provider Changes ---
        self.analysis_llm_var.trace_add("write", lambda *args: self.update_model_dropdowns("analysis"))
        self.gatekeeper_llm_var.trace_add("write", lambda *args: self.update_model_dropdowns("gatekeeper")) # Added
        self.writing_llm_var.trace_add("write", lambda *args: self.update_model_dropdowns("writing"))
        self.critic_llm_var.trace_add("write", lambda *args: self.update_model_dropdowns("critic"))

        # --- Initial Update of Model Dropdowns ---
        self.update_model_dropdowns("analysis")
        self.update_model_dropdowns("gatekeeper") # Added
        self.update_model_dropdowns("writing")
        self.update_model_dropdowns("critic")

        # --- Initial Update of Run Button Text ---
        self.update_run_button_text() # Call this after UI elements are created

        # --- Initial Update of Posting Platforms ---
        self._update_posting_platforms() # Call after UI elements are created

    def _update_posting_platforms(self):
        """
        Updates the posting platform selector based on configured and connected platforms.
        Reads connection_ok status for both Shopify and WordPress from the config.
        Builds a list of only the platforms marked as connected (True).
        Updates the posting_platform_selector values with this list.
        Enables/disables the selector and checkbox based on whether any platforms are available.
        """
        available_platforms = []
        
        # Check Shopify connection status
        # Use load_config().get() instead of the non-existent get_setting
        config = load_config()
        shopify_ok = config.get('SHOPIFY', 'connection_ok', fallback='False').lower() == 'true'
        if shopify_ok:
            available_platforms.append("Shopify")
            
        # Check WordPress connection status
        # Use load_config().get() again
        # config variable is already loaded from the Shopify check above
        wordpress_ok = config.get('WORDPRESS', 'connection_ok', fallback='False').lower() == 'true'
        if wordpress_ok:
            available_platforms.append("WordPress")
            
        # Update the platform selector with available platforms
        self.posting_platform_selector.config(values=available_platforms)
        
        # If we have platforms, enable the selector and set the first one as default
        if available_platforms:
            self.posting_platform_selector.config(state="readonly")
            self.posting_platform_var.set(available_platforms[0])  # Set first available as default
            self.enable_posting_var.set(True)  # Enable posting checkbox by default
        else:
            # No platforms available, disable selector and checkbox
            self.posting_platform_selector.config(state="disabled")
            self.posting_platform_var.set("")  # Clear selection
            self.enable_posting_var.set(False)  # Disable posting checkbox
            
        self.log_message(f"Updated posting platforms: {', '.join(available_platforms) if available_platforms else 'None available'}")


    def load_pipeline_settings(self):
        """Load settings from config manager."""
        self.pipeline_settings = config_manager.get_all_pipeline_settings()
        # Also load API keys and prompts if needed directly here, though likely needed more in the pipeline steps
        
        # Update UI variables based on saved settings
        try:
            # SERP API call limit
            if 'serp_api_max_calls' in self.pipeline_settings:
                max_calls = int(self.pipeline_settings['serp_api_max_calls'])
                self.serp_limit_var.set(max_calls)

            # Pipeline settings
            # Load the new dropdown setting, default to SERP API
            self.serp_source_var.set(self.pipeline_settings.get('serp_source', "Use SERP API (Recommended)"))
            # self.enable_serp_var.set(self.pipeline_settings.get('enable_serp_fetch', True)) # Removed old checkbox setting
            self.enable_analysis_var.set(self.pipeline_settings.get('enable_llm_analysis', True))
            self.enable_gatekeeper_var.set(self.pipeline_settings.get('enable_gatekeeper', False)) # Added
            self.enable_writing_var.set(self.pipeline_settings.get('enable_blog_writing', True))
            self.enable_review_var.set(self.pipeline_settings.get('enable_blog_review', True))
            self.enable_posting_var.set(self.pipeline_settings.get('enable_shopify_post', True)) # Note: Key name might need update if supporting WP posting toggle later

            # LLM provider selections
            self.analysis_llm_var.set(self.pipeline_settings.get('analysis_llm', 'local'))
            self.gatekeeper_llm_var.set(self.pipeline_settings.get('gatekeeper_llm', 'openai')) # Added
            self.writing_llm_var.set(self.pipeline_settings.get('writing_llm', 'openai'))
            self.critic_llm_var.set(self.pipeline_settings.get('critic_llm', 'openai'))

            # LLM specific model selections
            self.analysis_model_var.set(self.pipeline_settings.get('analysis_llm_model', ''))
            self.gatekeeper_model_var.set(self.pipeline_settings.get('gatekeeper_llm_model', '')) # Added
            self.writing_model_var.set(self.pipeline_settings.get('writing_llm_model', ''))
            self.critic_model_var.set(self.pipeline_settings.get('critic_llm_model', ''))

            # Other saved settings if available
            if 'blog_word_count' in self.pipeline_settings:
                self.word_count_var.set(int(self.pipeline_settings.get('blog_word_count', 1000)))
            if 'blog_tone' in self.pipeline_settings:
                self.tone_var.set(self.pipeline_settings.get('blog_tone', 'Professional'))
            if 'shopify_publish_immediately' in self.pipeline_settings:
                publish = self.pipeline_settings.get('shopify_publish_immediately', 'False')
                self.publish_immediately_var.set(publish.lower() == 'true')
            if 'shopify_additional_tags' in self.pipeline_settings:
                self.additional_tags_var.set(self.pipeline_settings.get('shopify_additional_tags', ''))
                
            # SERP options
            if 'serp_type' in self.pipeline_settings:
                self.serp_type_var.set(self.pipeline_settings.get('serp_type', 'Google Search'))
            if 'serp_country_code' in self.pipeline_settings:
                self.country_code_var.set(self.pipeline_settings.get('serp_country_code', 'us'))
            if 'serp_language_code' in self.pipeline_settings:
                self.language_code_var.set(self.pipeline_settings.get('serp_language_code', 'en'))
                
            # Scheduling options
            if 'enable_scheduling' in self.pipeline_settings:
                enable_scheduling = self.pipeline_settings.get('enable_scheduling', 'False')
                self.enable_scheduling_var.set(enable_scheduling if isinstance(enable_scheduling, bool) else enable_scheduling.lower() == 'true')
            if 'run_count' in self.pipeline_settings:
                self.run_count_var.set(int(self.pipeline_settings.get('run_count', 1)))
            if 'run_interval' in self.pipeline_settings:
                self.run_interval_var.set(int(self.pipeline_settings.get('run_interval', 20)))
            if 'interval_unit' in self.pipeline_settings:
                self.interval_unit_var.set(self.pipeline_settings.get('interval_unit', 'minutes'))
            # SEO Meta
            if 'meta_description' in self.pipeline_settings:
                self.meta_description_var.set(self.pipeline_settings.get('meta_description', ''))
            if 'upload_meta_description' in self.pipeline_settings:
                 upload_meta = self.pipeline_settings.get('upload_meta_description', 'False')
                 self.upload_meta_description_var.set(upload_meta if isinstance(upload_meta, bool) else upload_meta.lower() == 'true')

        except (ValueError, TypeError) as e:
            print(f"Error loading settings: {e}")
            # Use default values if config values are invalid

    def save_runtime_settings(self):
        """Save checkbox states and LLM selections before running (or in settings)."""
        settings_to_update = {
            # 'PIPELINE_SETTINGS.enable_serp_fetch': self.enable_serp_var.get(), # Removed old checkbox setting
            'PIPELINE_SETTINGS.serp_source': self.serp_source_var.get(), # Save dropdown selection
            'PIPELINE_SETTINGS.enable_llm_analysis': self.enable_analysis_var.get(),
            'PIPELINE_SETTINGS.enable_gatekeeper': self.enable_gatekeeper_var.get(), # Added
            'PIPELINE_SETTINGS.enable_blog_writing': self.enable_writing_var.get(),
            'PIPELINE_SETTINGS.enable_blog_review': self.enable_review_var.get(),
            'PIPELINE_SETTINGS.enable_shopify_post': self.enable_posting_var.get(), # Note: Key name might need update
            'PIPELINE_SETTINGS.analysis_llm': self.analysis_llm_var.get(),
            'PIPELINE_SETTINGS.gatekeeper_llm': self.gatekeeper_llm_var.get(), # Added
            'PIPELINE_SETTINGS.writing_llm': self.writing_llm_var.get(),
            'PIPELINE_SETTINGS.critic_llm': self.critic_llm_var.get(),
            'PIPELINE_SETTINGS.analysis_llm_model': self.analysis_model_var.get(),
            'PIPELINE_SETTINGS.gatekeeper_llm_model': self.gatekeeper_model_var.get(), # Added
            'PIPELINE_SETTINGS.writing_llm_model': self.writing_model_var.get(),
            'PIPELINE_SETTINGS.critic_llm_model': self.critic_model_var.get(),
            'PIPELINE_SETTINGS.serp_api_max_calls': self.serp_limit_var.get(),
            # Save advanced options
            'PIPELINE_SETTINGS.blog_word_count': self.word_count_var.get(),
            'PIPELINE_SETTINGS.blog_tone': self.tone_var.get(),
            'PIPELINE_SETTINGS.shopify_publish_immediately': str(self.publish_immediately_var.get()),
            'PIPELINE_SETTINGS.shopify_additional_tags': self.additional_tags_var.get(),
            # SERP options
            'PIPELINE_SETTINGS.serp_type': self.serp_type_var.get(),
            'PIPELINE_SETTINGS.serp_country_code': self.country_code_var.get(),
            'PIPELINE_SETTINGS.serp_language_code': self.language_code_var.get(),
            # Scheduling options
            'PIPELINE_SETTINGS.enable_scheduling': self.enable_scheduling_var.get(),
            'PIPELINE_SETTINGS.run_count': self.run_count_var.get(),
            'PIPELINE_SETTINGS.run_interval': self.run_interval_var.get(),
            'PIPELINE_SETTINGS.interval_unit': self.interval_unit_var.get(),
            # SEO Meta
            'PIPELINE_SETTINGS.meta_description': self.meta_description_var.get(),
            'PIPELINE_SETTINGS.upload_meta_description': self.upload_meta_description_var.get(),
        }
        config_manager.update_multiple_settings(settings_to_update)
        self.log_message("Runtime settings saved.")


    def log_message(self, message):
        """Appends a message to the output text area and updates status bar."""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, message + "\n")
        self.output_text.see(tk.END) # Scroll to the bottom
        self.output_text.config(state=tk.DISABLED)
        self.status_var.set(message)  # Update status bar
        self.master.update_idletasks() # Ensure GUI updates

    def run_pipeline(self):
        """Start the pipeline execution in a separate thread to prevent UI freezing."""
        # Disable the Run button to prevent multiple pipeline runs
        self.run_button.config(state=tk.DISABLED)
        
        # Check if scheduling is enabled
        if self.enable_scheduling_var.get():
            # If a scheduled run is already active, cancel it
            if self.scheduled_run_active:
                if self.schedule_timer:
                    self.master.after_cancel(self.schedule_timer)
                    self.schedule_timer = None
                # Cancel countdown timer if active
                if self.countdown_timer_id:
                    self.master.after_cancel(self.countdown_timer_id)
                    self.countdown_timer_id = None
                self.countdown_var.set("") # Clear countdown label
                self.scheduled_run_active = False
                self.log_message("Scheduled runs cancelled.")
                self.update_run_button_text() # Update button text
                self.keyword_entry.config(state=tk.NORMAL) # Re-enable keyword entry on cancel
                self.run_button.config(state=tk.NORMAL)
                return

            # Start a new scheduled run
            self.scheduled_run_active = True
            self.keyword_entry.config(state=tk.DISABLED) # Disable keyword entry during scheduled run
            self.current_run_count = 0
            self.current_batch_index = 0 # Reset batch index at the start
            if self.batch_keywords:
                self.log_message(f"Starting scheduled pipeline runs (Batch Mode: {len(self.batch_keywords)} keywords)...")
            else:
                self.log_message("Starting scheduled pipeline runs...")
            # Start the first run immediately
            self._schedule_next_run()
        else:
            # Start the pipeline in a separate thread (single run)
            # Ensure batch index is reset even for single runs if batch list exists
            self.current_batch_index = 0
            threading.Thread(target=self._execute_pipeline, daemon=True).start()
    
    def _schedule_next_run(self):
        """Schedule the next pipeline run based on the interval settings."""
        # Increment the run counter
        self.current_run_count += 1
        
        # Get the max run count (0 means indefinite)
        max_runs = self.run_count_var.get()
        
        # Log the current run number
        if max_runs > 0:
            self.log_message(f"Starting scheduled run {self.current_run_count} of {max_runs}...")
        else:
            self.log_message(f"Starting scheduled run {self.current_run_count} (indefinite mode)...")
        
        # Start the pipeline in a separate thread
        threading.Thread(target=self._execute_scheduled_pipeline, daemon=True).start()
    
    def _execute_scheduled_pipeline(self):
        """Execute the pipeline for the current scheduled run and schedule the next if needed."""
        pipeline_success = False
        try:
            keyword_to_process = None
            # Determine keyword: Use batch if active and keywords exist
            if self.scheduled_run_active and self.batch_keywords:
                if self.current_batch_index < len(self.batch_keywords):
                    keyword_to_process = self.batch_keywords[self.current_batch_index]
                    self.log_message(f"Processing batch keyword {self.current_batch_index + 1}/{len(self.batch_keywords)}: {keyword_to_process}")
                else:
                    # Should not happen if index is reset correctly, but handle defensively
                    self.log_message("Batch index out of range, resetting.")
                    self.current_batch_index = 0
                    keyword_to_process = self.batch_keywords[self.current_batch_index]
                    self.log_message(f"Processing batch keyword {self.current_batch_index + 1}/{len(self.batch_keywords)}: {keyword_to_process}")
            else:
                # Use the keyword from the main entry field if not in batch mode
                keyword_to_process = self.keyword_var.get()

            # Run the pipeline with the determined keyword
            pipeline_success = self._execute_pipeline(keyword_to_use=keyword_to_process)

            # Increment batch index ONLY if the pipeline was successful and in batch mode
            if pipeline_success and self.scheduled_run_active and self.batch_keywords:
                self.current_batch_index += 1
                # Loop back if we've processed all keywords in the batch
                if self.current_batch_index >= len(self.batch_keywords):
                    self.log_message("Completed one pass through batch keywords.")
                    self.current_batch_index = 0 # Reset for the next full run cycle

            # Check if we should schedule another run
            max_runs = self.run_count_var.get()
            if pipeline_success and (max_runs == 0 or self.current_run_count < max_runs) and self.scheduled_run_active:
                # Calculate interval in seconds for countdown
                interval_value = self.run_interval_var.get()
                interval_unit = self.interval_unit_var.get()
                interval_seconds = interval_value * 60 if interval_unit == "minutes" else interval_value
                interval_ms = interval_seconds * 1000

                # Log and start countdown
                self.log_message(f"Pipeline for '{keyword_to_process}' completed. Next run in {interval_value} {interval_unit}...")
                self._start_countdown(interval_seconds)

                # Schedule the next actual run execution
                self.schedule_timer = self.master.after(interval_ms, self._schedule_next_run)
            elif not pipeline_success:
                 # Don't schedule next run if pipeline failed
                 self.log_message(f"Pipeline failed for keyword: {keyword_to_process}. Stopping scheduled runs.")
                 self.scheduled_run_active = False
                 self.update_run_button_text()
                 self.keyword_entry.config(state=tk.NORMAL) # Re-enable on failure
                 self.run_button.config(state=tk.NORMAL)
            else:
                # All scheduled runs completed (reached max_runs) or stopped manually
                self.scheduled_run_active = False
                if pipeline_success: # Only log completion if it wasn't stopped by failure
                    self.log_message("All scheduled runs completed.")
                self.update_run_button_text()
                self.keyword_entry.config(state=tk.NORMAL) # Re-enable on completion
                self.run_button.config(state=tk.NORMAL) # Re-enable the Run button

        except Exception as e:
            self.log_message(f"Error in scheduled pipeline execution: {e}")
            self.scheduled_run_active = False
            self.update_run_button_text()
            self.keyword_entry.config(state=tk.NORMAL) # Re-enable on exception
            self.run_button.config(state=tk.NORMAL) # Re-enable the Run button
            # Ensure countdown is cleared on error
            if self.countdown_timer_id:
                self.master.after_cancel(self.countdown_timer_id)
                self.countdown_timer_id = None
            self.countdown_var.set("")

    def _execute_pipeline(self, keyword_to_use=None):
        """Main pipeline execution logic. Returns True on success, False on failure."""
        pipeline_success = False # Track success within this run
        try:
            # Determine the keyword to use for this specific run
            keyword = keyword_to_use if keyword_to_use is not None else self.keyword_var.get()

            if not keyword:
                 # Check if we are in batch mode and just failed to get a keyword (shouldn't happen)
                 if keyword_to_use is None:
                     messagebox.showerror("Error", "Please enter a seed keyword or use the Batch Keywords feature.")
                     self.log_message("Pipeline aborted: No keyword provided.")
                     self.status_var.set("Ready - Pipeline aborted: No keyword provided")
                 else: # Error getting keyword from batch list (unexpected)
                     self.log_message(f"Pipeline aborted: Invalid keyword provided for run: {keyword_to_use}")
                     self.status_var.set("Ready - Pipeline aborted: Invalid keyword")
                 # Don't re-enable run button here if called from scheduled task
                 return False # Indicate failure

            # Log start only if not called from scheduled task (which logs its own start)
            if keyword_to_use is None: # i.e., single manual run
                 self.log_message(f"Starting pipeline for keyword: {keyword}")
                 self.status_var.set(f"Starting pipeline for keyword: {keyword}")
            # Status for scheduled task is set in _execute_scheduled_pipeline

            self.save_runtime_settings() # Save current selections

            # --- Get settings ---

            # --- Get settings ---
            analysis_llm_provider = self.analysis_llm_var.get()
            writing_llm_provider = self.writing_llm_var.get()
            analysis_llm_model = self.analysis_model_var.get() # Get specific model
            writing_llm_model = self.writing_model_var.get() # Get specific model

            # --- Execute Steps based on Selections ---
            serp_data = None # Will hold data from SERP API or LLM fallback
            keyword_data_source = self.serp_source_var.get() # Get selected source

            # Step 1: Get Keywords/PAA (SERP API or LLM Fallback)
            if keyword_data_source == "Use SERP API (Recommended)":
                self.log_message("Step 1: Fetching SERP data via API...")
                self.status_var.set("Step 1: Fetching SERP data via API...")
                try:
                    api_key = config_manager.get_api_key('serpapi_key')
                    if not api_key or 'YOUR_' in api_key:
                         raise ValueError("SERP API key not configured.")
                    
                    # Get the max_calls setting
                    max_calls = self.serp_limit_var.get()
                    # If max_calls is 0, it means no limit, so pass None
                    max_calls_param = None if max_calls == 0 else max_calls
                    
                    self.log_message(f"SERP API call limit: {max_calls_param if max_calls_param is not None else 'No limit'}")  
                    
                    # Get country and language codes
                    country_code = self.country_code_var.get()
                    language_code = self.language_code_var.get()
                    self.log_message(f"Using country code: {country_code}, language code: {language_code}")
                    
                    # Check which SERP API to use
                    serp_type = self.serp_type_var.get()
                    self.log_message(f"Using SERP API type: {serp_type}")
                    
                    if serp_type == "Google Autocomplete":
                        serp_data = serp_fetcher.fetch_autocomplete_data(
                            query=keyword, 
                            api_key=api_key, 
                            hl=language_code, 
                            gl=country_code
                        )
                        self.log_message("Google Autocomplete data fetched successfully.")
                        if 'suggestions' in serp_data:
                            self.log_message(f"Found {len(serp_data.get('suggestions', []))} suggestions.")
                        else:
                            self.log_message("No suggestions found.")
                    else:  # Default to Google Search
                        serp_data = serp_fetcher.fetch_serp_data(
                            query=keyword, 
                            api_key=api_key, 
                            max_calls=max_calls_param,
                            hl=language_code,
                            gl=country_code
                        )
                        self.log_message("SERP data fetched successfully.")
                        self.log_message(f"Found {len(serp_data.get('organic_results', []))} organic results.")
                        self.log_message(f"Found {len(serp_data.get('related_questions', []))} related questions.")
                        self.log_message(f"Found {len(serp_data.get('related_searches', []))} related searches.")
                    
                    # Display the SERP data in the SERP tab
                    import json
                    self.serp_data_text.delete('1.0', tk.END)
                    self.serp_data_text.insert('1.0', json.dumps(serp_data, indent=2))
                    # Switch to the SERP data tab to show the data
                    for i, tab_id in enumerate(self.output_notebook.tabs()):
                        if self.output_notebook.tab(tab_id, "text") == "SERP Data":
                            self.output_notebook.select(i)
                            break
                    self.status_var.set("SERP data fetched successfully")

                    # --- Populate Keyword Ideas Tab ---
                    self._update_keyword_ideas_tab(serp_data, serp_type)

                except Exception as e:
                    self.log_message(f"Error fetching SERP data via API: {e}")
                    self.status_var.set(f"Error: Failed during SERP API fetch")
                    messagebox.showerror("Pipeline Error", f"Failed during SERP API fetch: {e}")
                    if keyword_to_use is None: self.run_button.config(state=tk.NORMAL)
                    return False # Indicate failure

            elif keyword_data_source == "Use Analysis LLM (Fallback)":
                self.log_message(f"Step 1: Generating Keywords/PAA using Analysis LLM ({analysis_llm_provider})...")
                self.status_var.set(f"Step 1: Generating Keywords/PAA via LLM ({analysis_llm_provider})...")
                try:
                    # --- LLM Fallback Logic ---
                    # 1. Get the analysis LLM instance
                    llm_provider = self.analysis_llm_var.get()
                    llm_model = self.analysis_model_var.get() or None
                    llm_api_key = config_manager.get_api_key(f"{llm_provider}_key") # Assumes key name format
                    llm_endpoint = config_manager.get_api_key("local_llm_endpoint") if llm_provider == "local" else None

                    llm_instance = get_llm_instance(
                        llm_type=llm_provider,
                        api_key=llm_api_key,
                        model_id=llm_model,
                        api_endpoint=llm_endpoint
                    )

                    # 2. Load the custom prompt from config or use default
                    # TODO: Add 'keyword_fallback_prompt' to settings window and config_manager
                    prompts = config_manager.get_all_prompts()
                    fallback_prompt_template = prompts.get('keyword_fallback_prompt', """You are an SEO assistant helping to brainstorm content ideas based on a seed keyword.
Given the seed keyword "{keyword}", generate the following:

1.  **Related Keywords:** Provide a list of 5-10 relevant related keywords (short and long-tail). Format each keyword on a new line starting with "- ".
2.  **People Also Ask (PAA):** Provide a list of 5-7 plausible "People Also Ask" questions related to the seed keyword. Format each question on a new line starting with "? ".
3.  **Brief Outline:** Provide a very brief 3-5 point outline for a blog post about the seed keyword. Format each point on a new line starting with "* ".

Seed Keyword: {keyword}

Output:""")

                    # 3. Construct the final prompt
                    final_prompt = fallback_prompt_template.format(keyword=keyword)

                    # 4. Call llm_instance.generate()
                    self.log_message(f"Calling {llm_provider} ({llm_model or 'default'}) to generate keyword ideas...")
                    llm_response_text = llm_instance.generate(prompt=final_prompt, max_tokens=600) # Adjust tokens as needed
                    self.log_message("LLM response received.")

                    # 5. Parse the LLM response
                    related_keywords_list = []
                    paa_questions_list = []
                    outline_points_list = []
                    parsing_successful = False # Flag to track parsing success

                    # --- Add validation for the LLM response ---
                    if not llm_response_text or llm_response_text.strip() == '\n"search_metadata"': # Check for empty or specific invalid response
                        self.log_message(f"Warning: LLM returned invalid or empty content: '{llm_response_text}'. Using placeholders.")
                        # Skip parsing and directly use placeholders (handled below if parsing_successful is False)
                    else:
                        # --- Attempt to parse the valid response ---
                        try:
                            for line in llm_response_text.splitlines():
                                line = line.strip()
                                if line.startswith("- "):
                                    related_keywords_list.append({"query": line[2:].strip()})
                                elif line.startswith("? "):
                                    paa_questions_list.append({"question": line[2:].strip()})
                                elif line.startswith("* "):
                                    outline_points_list.append(line) # Keep full line for outline

                            # Check if parsing yielded results
                            if related_keywords_list or paa_questions_list or outline_points_list:
                                parsing_successful = True
                                self.log_message("LLM response parsed successfully.")
                            else:
                                self.log_message("Warning: LLM response parsed, but no keywords, PAA, or outline found in the expected format.")
                                # Proceed, but might result in empty lists

                        except Exception as parse_error:
                            self.log_message(f"Error parsing LLM response content: {parse_error}. Using placeholders.")
                            # Ensure flag remains False

                    # --- Use placeholders if parsing failed or response was invalid ---
                    if not parsing_successful:
                        paa_questions_list = [{"question": f"What is {keyword}?"}]
                        related_keywords_list = [{"query": f"{keyword} benefits"}]
                        outline_points_list = [f"* Introduction to {keyword}"]
                        self.log_message("Generated placeholder keywords, PAA, and outline.")


                    # 6. Store the parsed data in the 'serp_data'-like variable
                    serp_data = {
                        "search_parameters": {"query": keyword, "engine": "llm_fallback", "llm_provider": llm_provider},
                        "related_questions": paa_questions_list,
                        "related_searches": related_keywords_list, # Use related_searches key
                        "organic_results": [], # Keep empty
                        "llm_raw_response": llm_response_text, # Store raw response for debugging/display
                        "llm_generated_outline": "\n".join(outline_points_list) # Store parsed outline
                    }

                    # Display the generated data in the SERP tab
                    self.serp_data_text.delete('1.0', tk.END)
                    # Show a structured summary + raw response
                    display_text = f"--- LLM Fallback Output for '{keyword}' ---\n\n"
                    display_text += "**Related Keywords:**\n" + "\n".join([f"- {item['query']}" for item in serp_data['related_searches']]) + "\n\n"
                    display_text += "**People Also Ask:**\n" + "\n".join([f"- {item['question']}" for item in serp_data['related_questions']]) + "\n\n"
                    display_text += "**Generated Outline:**\n" + serp_data['llm_generated_outline'] + "\n\n"
                    display_text += "--- Raw LLM Response ---\n" + serp_data['llm_raw_response']
                    self.serp_data_text.insert('1.0', display_text)

                    # Switch to the SERP data tab
                    for i, tab_id in enumerate(self.output_notebook.tabs()):
                        if self.output_notebook.tab(tab_id, "text") == "SERP Data":
                            self.output_notebook.select(i)
                            break

                    # Update Keyword Ideas tab with placeholder data
                    self._update_keyword_ideas_tab(serp_data, "LLM Fallback")

                    self.log_message("LLM Fallback Keyword/PAA generation step completed (placeholder).")
                    self.status_var.set("LLM Fallback Keyword/PAA generation completed")

                except Exception as e:
                    # Provide more context in the error message
                    import traceback
                    error_details = traceback.format_exc()
                    self.log_message(f"Error during LLM Keyword/PAA generation step: {type(e).__name__} - {e}")
                    self.log_message(f"Traceback:\n{error_details}")
                    self.status_var.set(f"Error: Failed during LLM Keyword/PAA generation")
                    messagebox.showerror("Pipeline Error", f"Failed during LLM Keyword/PAA generation: {type(e).__name__} - {e}")
                    if keyword_to_use is None: self.run_button.config(state=tk.NORMAL)
                    return False # Indicate failure
            else:
                 # Should not happen with the combobox
                 self.log_message("Error: Invalid keyword data source selected.")
                 return False

            # Step 2: Analyze Data (from SERP API or LLM)
            analysis_results = None
            if self.enable_analysis_var.get():
                # Adjust log message based on source
                analysis_source = "SERP data" if keyword_data_source == "Use SERP API (Recommended)" else "LLM-generated keyword data"
                self.log_message(f"Step 2: Analyzing {analysis_source} using {analysis_llm_provider}...")
                self.status_var.set(f"Step 2: Analyzing {analysis_source} using {analysis_llm_provider}...")

                if serp_data: # Check if we have data from Step 1 (either source)
                    try:
                        # Pass both provider and specific model if available
                        # The llm_analyzer function might need adjustment if the LLM fallback
                        # data structure is significantly different from real SERP data.
                        # For now, assume it can handle the basic structure created above.
                        analysis_results = llm_analyzer.analyze_serp_data(
                            serp_data=serp_data, # Pass data from Step 1
                            keyword=keyword,
                            llm_type=analysis_llm_provider,
                            model_id=analysis_llm_model or None
                        )
                        self.log_message("Data analysis completed successfully.")
                        # Show a preview of the analysis in the log
                        if 'raw_analysis' in analysis_results:
                            preview = analysis_results['raw_analysis'][:200] + "..." if len(analysis_results['raw_analysis']) > 200 else analysis_results['raw_analysis']
                            self.log_message(f"Analysis preview: {preview}")

                            # Display the full analysis in the Analysis tab
                            self.analysis_text.delete('1.0', tk.END)
                            self.analysis_text.insert('1.0', analysis_results['raw_analysis'])
                            # Switch to the Analysis tab
                            for i, tab_id in enumerate(self.output_notebook.tabs()):
                                if self.output_notebook.tab(tab_id, "text") == "Analysis":
                                    self.output_notebook.select(i)
                                    break
                        self.status_var.set("Data analysis completed successfully")
                    except Exception as e:
                        self.log_message(f"Error analyzing data: {e}")
                        self.status_var.set(f"Error: Failed during LLM analysis")
                        messagebox.showerror("Pipeline Error", f"Failed during LLM analysis: {e}")
                        if keyword_to_use is None: self.run_button.config(state=tk.NORMAL)
                        return False # Indicate failure
                elif keyword_data_source == "Use Analysis LLM (Fallback)" and serp_data.get("llm_generated_outline"):
                     # If source was LLM fallback, use the generated outline directly as analysis
                     self.log_message("Using LLM-generated outline as analysis result.")
                     analysis_results = {
                         'raw_analysis': serp_data["llm_generated_outline"],
                         'keywords': [item['query'] for item in serp_data.get('related_searches', [])], # Extract keywords
                         'paa': [item['question'] for item in serp_data.get('related_questions', [])] # Extract PAA
                     }
                     # Display the outline in the Analysis tab
                     self.analysis_text.delete('1.0', tk.END)
                     self.analysis_text.insert('1.0', analysis_results['raw_analysis'])
                     # Switch to the Analysis tab
                     for i, tab_id in enumerate(self.output_notebook.tabs()):
                         if self.output_notebook.tab(tab_id, "text") == "Analysis":
                             self.output_notebook.select(i)
                             break
                     self.status_var.set("Used LLM-generated outline as analysis")
                else:
                    self.log_message("Skipping analysis: No data available from Step 1.")
                    self.status_var.set("Skipping analysis: No data available")
            else:
                self.log_message("Step 2: Skipped analysis.")

            # --- Step 2.5: Gatekeeper Pre-Writing Check ---
            # Always run the pre-writing check regardless of checkbox state
            gatekeeper_feedback = "" # Initialize feedback string
            gatekeeper_check_ran = False # Track if check was actually performed
            
            # Log based on checkbox state (for UI visibility)
            if self.enable_gatekeeper_var.get():
                self.log_message("Step 2.5: Running Gatekeeper pre-writing check...")
            else:
                self.log_message("Step 2.5: Running Gatekeeper pre-writing check (always runs)...")
                
            self.status_var.set("Step 2.5: Running Gatekeeper pre-writing check...")
            
            if analysis_results: # Gatekeeper needs analysis output
                try:
                    gatekeeper_llm_provider = self.gatekeeper_llm_var.get()
                    gatekeeper_llm_model = self.gatekeeper_model_var.get()
                    # Determine the platform to check against (use the selected posting platform)
                    target_platform = self.posting_platform_var.get()
                    if not target_platform:
                         self.log_message("Gatekeeper Warning: No posting platform selected. Cannot check existing posts.")
                    else:
                        gatekeeper_feedback = gatekeeper.run_gatekeeper_check(
                            keyword=keyword,
                            analysis_results=analysis_results,
                            platform=target_platform,
                            llm_type=gatekeeper_llm_provider,
                            model_id=gatekeeper_llm_model or None
                        )
                        gatekeeper_check_ran = True
                        self.log_message("Gatekeeper pre-writing check completed.")
                        # Display feedback in the Gatekeeper tab
                        self.gatekeeper_feedback_text.delete('1.0', tk.END)
                        self.gatekeeper_feedback_text.insert('1.0', "--- PRE-WRITING CHECK ---\n\n" + 
                                                            (gatekeeper_feedback if gatekeeper_feedback else 
                                                             "Gatekeeper: No specific feedback provided (content likely unique or check skipped)."))
                        # Switch to the Gatekeeper tab if checkbox is enabled (otherwise don't disrupt user's tab)
                        if self.enable_gatekeeper_var.get():
                            for i, tab_id in enumerate(self.output_notebook.tabs()):
                                if self.output_notebook.tab(tab_id, "text") == "Gatekeeper Feedback":
                                    self.output_notebook.select(i)
                                    break
                        self.status_var.set("Gatekeeper pre-writing check completed")

                except Exception as e:
                    self.log_message(f"Error during Gatekeeper pre-writing check: {e}")
                    self.status_var.set("Error during Gatekeeper pre-writing check, continuing...")
                    # Display error in feedback tab as well
                    self.gatekeeper_feedback_text.delete('1.0', tk.END)
                    self.gatekeeper_feedback_text.insert('1.0', "--- PRE-WRITING CHECK ERROR ---\n\n" + f"Gatekeeper Error:\n{e}")
                    # Continue pipeline even if gatekeeper fails
            else:
                self.log_message("Skipping Gatekeeper pre-writing check: Analysis results not available.")
                self.status_var.set("Skipping Gatekeeper pre-writing check: No analysis results")
                # Clear feedback tab
                self.gatekeeper_feedback_text.delete('1.0', tk.END)
                self.gatekeeper_feedback_text.insert('1.0', "Gatekeeper pre-writing check skipped: No analysis results available.")


            # --- Step 3: Write Blog Post ---
            blog_result = None
            if self.enable_writing_var.get():
                self.log_message(f"Step 3: Writing blog post using {writing_llm_provider}...")
                self.status_var.set(f"Step 3: Writing blog post using {writing_llm_provider}...")
                if analysis_results:
                    try:
                        # Get word count and tone from UI
                        word_count = self.word_count_var.get()
                        tone = self.tone_var.get()
                        
                        self.log_message(f"Using word count: {word_count}, tone: {tone}")
                        
                        # Pass both provider and specific model if available
                        # Also pass gatekeeper feedback
                        blog_result = blog_writer.write_blog_post(
                            analysis_results=analysis_results,
                            keyword=keyword,
                            llm_type=writing_llm_provider,
                            model_id=writing_llm_model or None,
                            word_count=word_count,
                            tone=tone,
                            gatekeeper_feedback=gatekeeper_feedback # Pass feedback here
                        )
                        self.log_message("Blog post written successfully (incorporating Gatekeeper feedback if provided).")
                        self.log_message(f"Title: {blog_result['title']}")
                        self.log_message(f"Word count: {blog_result['actual_word_count']}")
                        
                        # Show a preview of the blog content in the log
                        preview = blog_result['content_html'][:200] + "..." if len(blog_result['content_html']) > 200 else blog_result['content_html']
                        self.log_message(f"Content preview: {preview}")
                        
                        # Display the full blog content in the Blog Content tab
                        self.blog_text.delete('1.0', tk.END)
                        self.blog_text.insert('1.0', blog_result['content_html'])
                        # Switch to the Blog Content tab
                        for i, tab_id in enumerate(self.output_notebook.tabs()):
                            if self.output_notebook.tab(tab_id, "text") == "Blog Content":
                                self.output_notebook.select(i)
                                break
                        self.status_var.set("Blog post written successfully")

                        # --- Populate Meta Description Field ---
                        if 'meta_description' in blog_result:
                            self.meta_description_var.set(blog_result['meta_description'])
                            self.log_message(f"Meta Description set: {blog_result['meta_description']}")
                        else:
                             self.meta_description_var.set("") # Clear if not found

                    except Exception as e:
                        self.log_message(f"Error writing blog post: {e}")
                        self.status_var.set(f"Error: Failed during blog writing")
                        messagebox.showerror("Pipeline Error", f"Failed during blog writing: {e}")
                        if keyword_to_use is None: self.run_button.config(state=tk.NORMAL)
                        return False # Indicate failure
                else:
                    self.log_message("Skipping blog writing: No analysis results available.")
                    self.status_var.set("Skipping blog writing: No analysis results available")
            else:
                self.log_message("Step 3: Skipped blog writing.")
                
            # --- Step 3.5: Gatekeeper Post-Writing Evaluation & Retry Logic ---
            gatekeeper_revision_feedback = "" # Store feedback if revision is needed
            gatekeeper_blocked_posting = False # Flag to block posting if final eval fails
            
            if blog_result and self.enable_gatekeeper_var.get():
                original_blog_result = blog_result.copy() # Keep a copy of the first draft
                
                # --- Initial Evaluation ---
                self.log_message("Step 3.5a: Running Gatekeeper Post-Writing Evaluation (Attempt 1)...")
                self.status_var.set("Step 3.5a: Running Gatekeeper Evaluation (Attempt 1)...")
                
                try:
                    gatekeeper_llm_provider = self.gatekeeper_llm_var.get()
                    gatekeeper_llm_model = self.gatekeeper_model_var.get()
                    target_platform = self.posting_platform_var.get() or "Shopify" # Default if none selected
                    
                    eval_result = gatekeeper.evaluate_blog_post(
                        blog_post_html=blog_result['content_html'],
                        keyword=keyword,
                        analysis_results=analysis_results,
                        platform=target_platform,
                        llm_type=gatekeeper_llm_provider,
                        model_id=gatekeeper_llm_model or None
                    )
                    
                    eval_status = eval_result.get('status', 'ERROR')
                    gatekeeper_revision_feedback = eval_result.get('feedback', 'No feedback provided.')
                    
                    self.log_message(f"Gatekeeper Evaluation (Attempt 1) Completed. Status: {eval_status}")
                    
                    # Update Gatekeeper Tab
                    current_feedback_text = self.gatekeeper_feedback_text.get('1.0', tk.END).strip()
                    self.gatekeeper_feedback_text.delete('1.0', tk.END)
                    eval_header = f"--- POST-WRITING EVALUATION (Attempt 1 - {eval_status}) ---"
                    self.gatekeeper_feedback_text.insert('1.0', f"{current_feedback_text}\n\n{eval_header}\n\n{gatekeeper_revision_feedback}")
                    
                    # Switch to Gatekeeper Tab
                    for i, tab_id in enumerate(self.output_notebook.tabs()):
                        if self.output_notebook.tab(tab_id, "text") == "Gatekeeper Feedback":
                            self.output_notebook.select(i)
                            break
                            
                except Exception as e:
                    self.log_message(f"Error during Gatekeeper Evaluation (Attempt 1): {e}")
                    self.status_var.set("Error during Gatekeeper Evaluation (Attempt 1), continuing...")
                    eval_status = "ERROR" # Treat error as permissive
                    gatekeeper_revision_feedback = f"Error during evaluation: {e}"
                    # Update Gatekeeper Tab with Error
                    current_feedback_text = self.gatekeeper_feedback_text.get('1.0', tk.END).strip()
                    self.gatekeeper_feedback_text.delete('1.0', tk.END)
                    eval_header = f"--- POST-WRITING EVALUATION (Attempt 1 - ERROR) ---"
                    self.gatekeeper_feedback_text.insert('1.0', f"{current_feedback_text}\n\n{eval_header}\n\n{gatekeeper_revision_feedback}")

                # --- Rewrite Attempt (if needed) ---
                if eval_status == 'REVISION_REQUIRED' and self.enable_writing_var.get():
                    self.log_message("Step 3.5b: Attempting Blog Rewrite based on Gatekeeper feedback...")
                    self.status_var.set("Step 3.5b: Attempting Blog Rewrite...")
                    
                    try:
                        word_count = self.word_count_var.get()
                        tone = self.tone_var.get()
                        
                        # Call blog_writer again for rewrite
                        blog_result = blog_writer.write_blog_post(
                            analysis_results=analysis_results,
                            keyword=keyword,
                            llm_type=writing_llm_provider,
                            model_id=writing_llm_model or None,
                            word_count=word_count,
                            tone=tone,
                            gatekeeper_feedback=gatekeeper_revision_feedback, # Pass revision feedback
                            is_rewrite=True,
                            previous_content=original_blog_result['content_html']
                        )
                        
                        self.log_message("Blog post rewritten successfully.")
                        self.log_message(f"Rewritten Title: {blog_result['title']}")
                        # Update Blog Content Tab
                        self.blog_text.delete('1.0', tk.END)
                        self.blog_text.insert('1.0', blog_result['content_html'])
                        # Update Meta Description if available
                        if 'meta_description' in blog_result:
                            self.meta_description_var.set(blog_result['meta_description'])
                        
                        # --- Second Evaluation ---
                        self.log_message("Step 3.5c: Running Gatekeeper Post-Writing Evaluation (Attempt 2)...")
                        self.status_var.set("Step 3.5c: Running Gatekeeper Evaluation (Attempt 2)...")
                        
                        try:
                            retry_eval_result = gatekeeper.evaluate_blog_post(
                                blog_post_html=blog_result['content_html'],
                                keyword=keyword,
                                analysis_results=analysis_results,
                                platform=target_platform,
                                llm_type=gatekeeper_llm_provider,
                                model_id=gatekeeper_llm_model or None
                            )
                            
                            retry_eval_status = retry_eval_result.get('status', 'ERROR')
                            retry_feedback = retry_eval_result.get('feedback', 'No feedback provided.')
                            
                            self.log_message(f"Gatekeeper Evaluation (Attempt 2) Completed. Status: {retry_eval_status}")
                            
                            # Update Gatekeeper Tab
                            current_feedback_text = self.gatekeeper_feedback_text.get('1.0', tk.END).strip()
                            self.gatekeeper_feedback_text.delete('1.0', tk.END)
                            retry_header = f"--- POST-WRITING EVALUATION (Attempt 2 - {retry_eval_status}) ---"
                            self.gatekeeper_feedback_text.insert('1.0', f"{current_feedback_text}\n\n{retry_header}\n\n{retry_feedback}")
                            
                            if retry_eval_status == 'REVISION_REQUIRED':
                                self.log_message("WARNING: Rewritten blog post STILL requires revision. Blocking posting.")
                                gatekeeper_blocked_posting = True
                                # Optionally revert blog_result to original for review? Or keep rewritten? Keeping rewritten for now.
                                # blog_result = original_blog_result 
                                # self.blog_text.delete('1.0', tk.END)
                                # self.blog_text.insert('1.0', original_blog_result['content_html'])
                                
                        except Exception as e_retry:
                            self.log_message(f"Error during Gatekeeper Evaluation (Attempt 2): {e_retry}")
                            self.status_var.set("Error during Gatekeeper Evaluation (Attempt 2), continuing...")
                            # Treat error as permissive, don't block posting
                            # Update Gatekeeper Tab with Error
                            current_feedback_text = self.gatekeeper_feedback_text.get('1.0', tk.END).strip()
                            self.gatekeeper_feedback_text.delete('1.0', tk.END)
                            retry_header = f"--- POST-WRITING EVALUATION (Attempt 2 - ERROR) ---"
                            self.gatekeeper_feedback_text.insert('1.0', f"{current_feedback_text}\n\n{retry_header}\n\nError: {e_retry}")

                    except Exception as e_rewrite:
                        self.log_message(f"Error during blog rewrite: {e_rewrite}")
                        self.status_var.set("Error during blog rewrite, continuing with original...")
                        blog_result = original_blog_result # Revert to original if rewrite fails
                        # Update Blog Content Tab
                        self.blog_text.delete('1.0', tk.END)
                        self.blog_text.insert('1.0', original_blog_result['content_html'])
                        # Update Meta Description if available
                        if 'meta_description' in original_blog_result:
                            self.meta_description_var.set(original_blog_result['meta_description'])
                            
            else:
                 # Log skipping if Gatekeeper disabled or no blog content
                 if blog_result:
                     self.log_message("Step 3.5: Skipped Gatekeeper post-writing evaluation (Gatekeeper disabled).")
                 else:
                     self.log_message("Step 3.5: Skipped Gatekeeper post-writing evaluation (no blog content).")

            # Step 4: Critique blog content
            review_result = None
            if self.enable_review_var.get():
                self.log_message(f"Step 4: Reviewing blog post using {self.critic_llm_var.get()}...")
                self.status_var.set(f"Step 4: Reviewing blog post using {self.critic_llm_var.get()}...")
                if blog_result and analysis_results and serp_data:
                    try:
                        # Get the critic LLM provider and model
                        critic_llm_provider = self.critic_llm_var.get()
                        critic_llm_model = self.critic_model_var.get()
                        
                        # Call the blog_critic module
                        review_result = blog_critic.critique_blog_content(
                            serp_data=serp_data,
                            analysis_results=analysis_results,
                            blog_content=blog_result,
                            keyword=keyword,
                            llm_type=critic_llm_provider,
                            model_id=critic_llm_model or None
                        )
                        
                        self.log_message("Blog review completed successfully.")
                        
                        # If there's an overall score, display it
                        if 'overall_score' in review_result:
                            self.log_message(f"Overall Score: {review_result['overall_score']}/100")
                        
                        # Display the review in the Review tab
                        self.review_text.delete('1.0', tk.END)
                        self.review_text.insert('1.0', review_result['raw_critique'])
                        
                        # Switch to the Review tab
                        for i, tab_id in enumerate(self.output_notebook.tabs()):
                            if self.output_notebook.tab(tab_id, "text") == "Review":
                                self.output_notebook.select(i)
                                break
                        self.status_var.set("Blog review completed successfully")
                                
                    except Exception as e:
                        self.log_message(f"Error reviewing blog post: {e}")
                        self.status_var.set(f"Error during blog review, continuing pipeline")
                        messagebox.showerror("Pipeline Error", f"Failed during blog review: {e}")
                        # Continue pipeline despite review error
                else:
                    self.log_message("Skipping blog review: Missing required data for review.")
                    self.status_var.set("Skipping blog review: Missing required data")
            else:
                self.log_message("Step 4: Skipped blog review.")

            # Step 5: Post Blog Article (Conditional Platform)
            if self.enable_posting_var.get():
                selected_platform = self.posting_platform_var.get()
                if not selected_platform:
                    self.log_message("Posting skipped: No platform selected or configured.")
                    self.status_var.set("Posting skipped: No platform selected")
                # Only proceed with posting if blog_result exists and not blocked by gatekeeper
                elif blog_result and not gatekeeper_blocked_posting:
                    self.log_message(f"Step 5: Posting to {selected_platform}...")
                    self.status_var.set(f"Step 5: Posting to {selected_platform}...")
                    try:
                        # Common settings
                        additional_tags = self.additional_tags_var.get()
                        publish_immediately = self.publish_immediately_var.get()
                        tags = keyword # Start with seed keyword as a tag
                        if additional_tags:
                            tags += ", " + additional_tags # Append additional tags

                        meta_desc_to_upload = None
                        upload_meta_flag = False

                        if selected_platform == "Shopify":
                            # Fetch Shopify specific settings
                            # Use load_config().get() for Shopify settings
                            config = load_config() # Reload config in case settings changed
                            shop_url = config.get('SHOPIFY', 'shop_url', fallback=None)
                            api_token = config.get('SHOPIFY', 'api_token', fallback=None)
                            upload_meta_flag = config.get('SHOPIFY', 'upload_meta_description', fallback='False').lower() == 'true'

                            if not shop_url or not api_token:
                                raise ValueError("Shopify URL or API Token not configured in Settings.")

                            if upload_meta_flag:
                                meta_desc_to_upload = self.meta_description_var.get()
                                self.log_message(f"Attempting Shopify meta description upload: {'Yes' if meta_desc_to_upload else 'No (field empty)'}")

                            self.log_message(f"Publishing to Shopify as: {'Published' if publish_immediately else 'Draft'}")
                            self.log_message(f"Tags: {tags}")

                            article = shopify_poster.post_blog_article(
                                shop_url=shop_url,
                                api_token=api_token,
                                title=blog_result['title'],
                                content_html=blog_result['content_html'],
                                tags=tags,
                                published=publish_immediately,
                                meta_description=meta_desc_to_upload
                            )
                            post_id = article.id
                            post_platform = "Shopify"

                        elif selected_platform == "WordPress":
                            # Fetch WordPress specific settings using load_config().get()
                            config = load_config() # Reload config
                            site_url = config.get('WORDPRESS', 'site_url', fallback=None)
                            username = config.get('WORDPRESS', 'username', fallback=None)
                            app_password = config.get('WORDPRESS', 'app_password', fallback=None)
                            upload_meta_flag = config.get('WORDPRESS', 'upload_meta_description', fallback='False').lower() == 'true'

                            if not site_url or not username or not app_password:
                                raise ValueError("WordPress Site URL, Username, or Application Password not configured in Settings.")

                            if upload_meta_flag:
                                meta_desc_to_upload = self.meta_description_var.get()
                                self.log_message(f"Attempting WordPress meta description upload: {'Yes' if meta_desc_to_upload else 'No (field empty)'}")

                            wp_status = 'publish' if publish_immediately else 'draft'
                            self.log_message(f"Publishing to WordPress as: {wp_status}")
                            self.log_message(f"Tags: {tags} (Note: Tag assignment might require existing tags on WP)")

                            post_info = wordpress_poster.post_blog_article(
                                site_url=site_url,
                                username=username,
                                app_password=app_password,
                                title=blog_result['title'],
                                content_html=blog_result['content_html'],
                                tags=tags,
                                published_status=wp_status,
                                meta_description=meta_desc_to_upload
                            )
                            post_id = post_info.get('id', 'N/A')
                            post_platform = "WordPress"

                        else:
                            raise ValueError(f"Unknown posting platform selected: {selected_platform}")

                        # Common success logging and optional popup
                        self.log_message(f"Successfully posted to {post_platform}! Post ID: {post_id}")
                        self.status_var.set(f"Successfully posted to {post_platform}! Post ID: {post_id}")
                        if keyword_to_use is None: # Only show popup for single manual runs
                            messagebox.showinfo("Success", f"Blog post successfully created on {post_platform}!\nTitle: {blog_result['title']}\nStatus: {'Published' if publish_immediately else 'Draft'}", parent=self.master)

                    except Exception as e:
                        self.log_message(f"Error posting to {selected_platform}: {e}")
                        self.status_var.set(f"Error: Failed during {selected_platform} posting")
                        messagebox.showerror("Pipeline Error", f"Failed during {selected_platform} posting: {e}", parent=self.master)
                        if keyword_to_use is None: self.run_button.config(state=tk.NORMAL)
                        return False # Indicate failure
                elif blog_result and gatekeeper_blocked_posting:
                    self.log_message("Posting skipped: Gatekeeper blocked posting after final revision check.")
                    self.status_var.set("Posting skipped: Gatekeeper blocked posting")
                else: # No blog_result
                    self.log_message("Posting skipped: No blog content available.")
                    self.status_var.set("Posting skipped: No blog content available")
            else:
                self.log_message("Step 5: Posting step disabled.")

            # --- Update Keyword Analysis Tab (if data available) ---
            if analysis_results and blog_result:
                 self._update_keyword_analysis_tab(keyword, analysis_results, blog_result)
            else:
                 # Clear the tab if required data is missing
                 self.keyword_analysis_text.delete('1.0', tk.END)
                 self.keyword_analysis_text.insert('1.0', "Keyword analysis requires both LLM Analysis and Blog Writing steps to be enabled and successful.")


            # If we reached here without returning False, it was successful
            pipeline_success = True
            # Log completion only for single runs or the final status update
            if keyword_to_use is None:
                self.log_message(f"Pipeline for '{keyword}' completed successfully!")
                self.status_var.set("Pipeline completed successfully!")
            else:
                # Update status bar specifically for scheduled runs completion
                self.status_var.set(f"Completed keyword: '{keyword}'. Waiting for next run...")

        except Exception as e:
            # Catch any unexpected exceptions
            self.log_message(f"Unexpected error in pipeline for keyword '{keyword}': {e}")
            self.status_var.set(f"Unexpected error for keyword '{keyword}'")
            messagebox.showerror("Pipeline Error", f"Unexpected error for keyword '{keyword}': {e}")
            pipeline_success = False # Indicate failure
        finally:
            # Re-enable run button ONLY if it was a single, manual run
            if keyword_to_use is None:
                self.run_button.config(state=tk.NORMAL)
                self.update_run_button_text() # Reset text if needed

        return pipeline_success # Return success status

    def update_model_dropdowns(self, dropdown_type):
        """Updates the model dropdown based on the selected provider."""
        if dropdown_type == "analysis":
            provider = self.analysis_llm_var.get()
            combo = self.analysis_model_combo
            model_var = self.analysis_model_var
        elif dropdown_type == "gatekeeper": # Added
            provider = self.gatekeeper_llm_var.get()
            combo = self.gatekeeper_model_combo
            model_var = self.gatekeeper_model_var
        elif dropdown_type == "writing":
            provider = self.writing_llm_var.get()
            combo = self.writing_model_combo
            model_var = self.writing_model_var
        elif dropdown_type == "critic": # Changed else to elif
            provider = self.critic_llm_var.get()
            combo = self.critic_model_combo
            model_var = self.critic_model_var
        else: # Should not happen
            return

        # Get available models for this provider
        available_models = AVAILABLE_MODELS.get(provider, [])
        
        if available_models:
            combo.config(values=available_models, state="readonly")
            # If current value not in available models, set to first available
            if model_var.get() not in available_models and available_models:
                model_var.set(available_models[0])
        else:
            combo.config(values=["No models available"], state="disabled")
            model_var.set("")

    def open_settings(self):
        """Opens the settings window."""
        settings_window = tk.Toplevel(self.master)
        app = SettingsWindow(settings_window)
        # Make the settings window modal
        settings_window.transient(self.master)
        settings_window.grab_set()
        self.master.wait_window(settings_window)
        # Reload settings after the settings window is closed
        self.load_pipeline_settings()
        # Update model dropdowns in case available models changed
        self.update_model_dropdowns("analysis")
        self.update_model_dropdowns("writing")
        self.update_model_dropdowns("critic") # Add critic back
        # Update available posting platforms AFTER settings window closes
        self._update_posting_platforms()

    def copy_to_clipboard(self, text_widget):
        """Copies the content of a text widget to the clipboard."""
        content = text_widget.get('1.0', tk.END)
        self.master.clipboard_clear()
        self.master.clipboard_append(content)
        self.log_message("Content copied to clipboard.")

    def save_to_file(self, text_widget, default_filename):
        """Saves the content of a text widget to a file."""
        from tkinter import filedialog
        import os
        
        content = text_widget.get('1.0', tk.END)
        if not content.strip():
            messagebox.showwarning("Empty Content", "There is no content to save.")
            return
            
        # Ask user for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=os.path.splitext(default_filename)[1],
            filetypes=[("All Files", "*.*"), 
                      ("Text Files", "*.txt"), 
                      ("HTML Files", "*.html"),
                      ("JSON Files", "*.json")],
            initialfile=default_filename
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_message(f"Content saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save file: {e}")

    def open_batch_keyword_dialog(self):
        """Opens a dialog to enter batch keywords."""
        dialog = tk.Toplevel(self.master)
        dialog.title("Enter Batch Keywords")
        dialog.geometry("400x300")
        dialog.transient(self.master)
        dialog.grab_set()

        ttk.Label(dialog, text="Paste keywords below (one per line):").pack(pady=(10, 5))

        text_area = scrolledtext.ScrolledText(dialog, wrap=tk.WORD, height=10)
        text_area.pack(fill=tk.BOTH, expand=True, padx=10)
        # Pre-fill with existing keywords if any
        text_area.insert('1.0', "\n".join(self.batch_keywords))

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        def on_ok():
            content = text_area.get('1.0', tk.END).strip()
            if content:
                # Split by newline, strip whitespace from each line, filter empty lines
                self.batch_keywords = [line.strip() for line in content.splitlines() if line.strip()]
            else:
                self.batch_keywords = []
            self.log_message(f"Loaded {len(self.batch_keywords)} batch keywords.")
            self.update_run_button_text()
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ok_button = ttk.Button(button_frame, text="OK", command=on_ok)
        ok_button.pack(side=tk.LEFT, padx=5)
        cancel_button = ttk.Button(button_frame, text="Cancel", command=on_cancel)
        cancel_button.pack(side=tk.LEFT, padx=5)

        self.master.wait_window(dialog)

    def update_run_button_text(self):
        """Updates the text of the Run Pipeline button based on batch mode."""
        if self.enable_scheduling_var.get() and self.batch_keywords:
            self.run_button.config(text=f"Run Pipeline (Batch: {len(self.batch_keywords)} Keywords)")
        else:
            self.run_button.config(text="Run Pipeline")

    def _start_countdown(self, seconds_remaining):
        """Starts or updates the visual countdown timer."""
        # Cancel any existing countdown timer first
        if self.countdown_timer_id:
            self.master.after_cancel(self.countdown_timer_id)
            self.countdown_timer_id = None

        if seconds_remaining > 0:
            self.countdown_var.set(f"Next run in: {seconds_remaining}s")
            # Store the ID returned by 'after' so we can cancel it
            self.countdown_timer_id = self.master.after(1000, lambda: self._start_countdown(seconds_remaining - 1))
        else:
            self.countdown_var.set("") # Clear the countdown label
            self.countdown_timer_id = None # Reset the timer ID

    def _update_keyword_ideas_tab(self, serp_data, serp_type):
        """Populates the Keyword Ideas tab with relevant data from SERP results."""
        self.keyword_ideas_text.delete('1.0', tk.END)
        output_lines = []

        if not serp_data:
            output_lines.append("No SERP data available to extract keyword ideas.")
            self.keyword_ideas_text.insert('1.0', "\n".join(output_lines))
            return

        # --- Autocomplete Suggestions ---
        if serp_type in ["Google Autocomplete", "Combined (Search + Autocomplete)"]:
            suggestions = serp_data.get('suggestions', [])
            if suggestions:
                output_lines.append("--- Autocomplete Suggestions ---")
                for item in suggestions:
                    output_lines.append(f"- {item.get('value', 'N/A')}")
                output_lines.append("\n") # Add spacing
            else:
                 output_lines.append("--- Autocomplete Suggestions ---\n(None found)\n")


        # --- Related Searches (from Google Search) ---
        related_searches = serp_data.get('related_searches', [])
        if related_searches:
            output_lines.append("--- Related Searches ---")
            for item in related_searches:
                output_lines.append(f"- {item.get('query', 'N/A')}")
            output_lines.append("\n") # Add spacing
        elif serp_type != "Google Autocomplete": # Only show 'None found' if we expected them
             output_lines.append("--- Related Searches ---\n(None found)\n")


        # --- People Also Ask (from Google Search) ---
        related_questions = serp_data.get('related_questions', [])
        if related_questions:
            output_lines.append("--- People Also Ask ---")
            for item in related_questions:
                output_lines.append(f"- {item.get('question', 'N/A')}")
            output_lines.append("\n") # Add spacing
        elif serp_type != "Google Autocomplete": # Only show 'None found' if we expected them
             output_lines.append("--- People Also Ask ---\n(None found)\n")

        if not output_lines or all(line.endswith("(None found)\n") for line in output_lines if line.startswith("---")):
             output_lines = ["No specific keyword ideas found in the current SERP data."]


        self.keyword_ideas_text.insert('1.0', "\n".join(output_lines))
        self.log_message("Keyword Ideas tab updated.")


    def _update_keyword_analysis_tab(self, keyword, analysis_results, blog_result):
        """Populates the Keyword Analysis tab."""
        self.keyword_analysis_text.delete('1.0', tk.END)
        output_lines = []

        if not analysis_results or not blog_result:
            output_lines.append("Missing analysis or blog content for keyword analysis.")
            self.keyword_analysis_text.insert('1.0', "\n".join(output_lines))
            return

        # --- Target Keywords ---
        output_lines.append("--- Target Keywords ---")
        output_lines.append(f"Seed Keyword: {keyword}")
        # Try to extract keywords from analysis (simple approach - look for a list)
        # This might need refinement based on the actual structure of analysis_results
        analysis_text = analysis_results.get('raw_analysis', '')
        # Example: Look for lines starting with "- " or similar list format
        potential_keywords = [line.strip('- ').strip() for line in analysis_text.split('\n') if line.strip().startswith('- ')]
        # Basic filtering (optional)
        target_keywords = [kw for kw in potential_keywords if len(kw) > 3 and len(kw) < 50] # Simple length filter
        if target_keywords:
             output_lines.append("Keywords from Analysis:")
             for kw in target_keywords[:10]: # Limit displayed keywords
                 output_lines.append(f"- {kw}")
        output_lines.append("\n")

        # --- Keyword Presence in Blog Content ---
        output_lines.append("--- Keyword Presence in Blog ---")
        content_html = blog_result.get('content_html', '')
        # Basic HTML stripping
        content_text = re.sub('<[^<]+?>', ' ', content_html) # Replace tags with space
        content_text = re.sub(r'\s+', ' ', content_text).strip().lower() # Normalize whitespace and case

        if not content_text:
            output_lines.append("Could not extract text content from blog HTML.")
        else:
            # Count seed keyword
            seed_keyword_lower = keyword.lower()
            seed_count = len(re.findall(r'\b' + re.escape(seed_keyword_lower) + r'\b', content_text))
            output_lines.append(f"'{keyword}': {seed_count} occurrences")

            # Count keywords from analysis
            counted_analysis_kws = set() # Avoid double counting if analysis lists duplicates
            if target_keywords:
                 output_lines.append("\nAnalysis Keywords:")
                 for kw in target_keywords[:10]: # Use the same limited list
                     kw_lower = kw.lower()
                     if kw_lower != seed_keyword_lower and kw_lower not in counted_analysis_kws:
                         count = len(re.findall(r'\b' + re.escape(kw_lower) + r'\b', content_text))
                         output_lines.append(f"- '{kw}': {count} occurrences")
                         counted_analysis_kws.add(kw_lower)

        self.keyword_analysis_text.insert('1.0', "\n".join(output_lines))
        self.log_message("Keyword Analysis tab updated.")
