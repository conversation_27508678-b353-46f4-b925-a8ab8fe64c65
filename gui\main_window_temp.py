    def run_pipeline(self):
        """Start the pipeline execution in a separate thread to prevent UI freezing."""
        # Disable the Run button to prevent multiple pipeline runs
        self.run_button.config(state=tk.DISABLED)
        
        # Start the pipeline in a separate thread
        threading.Thread(target=self._execute_pipeline, daemon=True).start()
        
    def _execute_pipeline(self):
        """Main pipeline execution logic."""
        try:
            self.log_message("Starting pipeline...")
            self.save_runtime_settings() # Save current selections

            keyword = self.keyword_var.get()
            if not keyword:
                messagebox.showerror("Error", "Please enter a seed keyword.")
                self.log_message("Pipeline aborted: No keyword entered.")
                self.run_button.config(state=tk.NORMAL)  # Re-enable the Run button
                return

            self.log_message(f"Keyword: {keyword}")

            # --- Get settings ---
            analysis_llm_provider = self.analysis_llm_var.get()
            writing_llm_provider = self.writing_llm_var.get()
            critic_llm_provider = self.critic_llm_var.get()
            analysis_llm_model = self.analysis_model_var.get() # Get specific model
            writing_llm_model = self.writing_model_var.get() # Get specific model
            critic_llm_model = self.critic_model_var.get() # Get specific model

            # --- Execute Steps based on Checkboxes ---
            serp_data = None
            if self.enable_serp_var.get():
                self.log_message("Step 1: Fetching SERP data...")
                try:
                    api_key = config_manager.get_api_key('serpapi_key')
                    if not api_key or 'YOUR_' in api_key:
                         raise ValueError("SERP API key not configured.")
                    
                    # Get the max_calls setting
                    max_calls = self.serp_limit_var.get()
                    # If max_calls is 0, it means no limit, so pass None
                    max_calls_param = None if max_calls == 0 else max_calls
                    
                    self.log_message(f"SERP API call limit: {max_calls_param if max_calls_param is not None else 'No limit'}")  
                    
                    # Get country and language codes
                    country_code = self.country_code_var.get()
                    language_code = self.language_code_var.get()
                    self.log_message(f"Using country code: {country_code}, language code: {language_code}")
                    
                    # Check which SERP API to use
                    serp_type = self.serp_type_var.get()
                    self.log_message(f"Using SERP API type: {serp_type}")
                    
                    if serp_type == "Google Autocomplete":
                        serp_data = serp_fetcher.fetch_autocomplete_data(
                            query=keyword, 
                            api_key=api_key, 
                            hl=language_code, 
                            gl=country_code
                        )
                        self.log_message("Google Autocomplete data fetched successfully.")
                        if 'suggestions' in serp_data:
                            self.log_message(f"Found {len(serp_data.get('suggestions', []))} suggestions.")
                        else:
                            self.log_message("No suggestions found.")
                    else:  # Default to Google Search
                        serp_data = serp_fetcher.fetch_serp_data(
                            query=keyword, 
                            api_key=api_key, 
                            max_calls=max_calls_param,
                            hl=language_code,
                            gl=country_code
                        )
                        self.log_message("SERP data fetched successfully.")
                        self.log_message(f"Found {len(serp_data.get('organic_results', []))} organic results.")
                        self.log_message(f"Found {len(serp_data.get('related_questions', []))} related questions.")
                        self.log_message(f"Found {len(serp_data.get('related_searches', []))} related searches.")
                    
                    # Display the SERP data in the SERP tab
                    import json
                    self.serp_data_text.delete('1.0', tk.END)
                    self.serp_data_text.insert('1.0', json.dumps(serp_data, indent=2))
                    # Switch to the SERP data tab to show the data
                    for i, tab_id in enumerate(self.output_notebook.tabs()):
                        if self.output_notebook.tab(tab_id, "text") == "SERP Data":
                            self.output_notebook.select(i)
                            break
                except Exception as e:
                    self.log_message(f"Error fetching SERP data: {e}")
                    messagebox.showerror("Pipeline Error", f"Failed during SERP fetch: {e}")
                    self.run_button.config(state=tk.NORMAL)  # Re-enable the Run button
                    return # Stop pipeline
            else:
                self.log_message("Step 1: Skipped SERP fetch.")

            analysis_results = None
            if self.enable_analysis_var.get():
                self.log_message(f"Step 2: Analyzing SERP data using {analysis_llm_provider}...")
                if serp_data:
                    try:
                        # Pass both provider and specific model if available
                        analysis_results = llm_analyzer.analyze_serp_data(
                            serp_data=serp_data,
                            keyword=keyword,
                            llm_type=analysis_llm_provider,
                            model_id=analysis_llm_model or None # Pass specific model or None
                        )
                        self.log_message("SERP data analyzed successfully.")
                        # Show a preview of the analysis in the log
                        if 'raw_analysis' in analysis_results:
                            preview = analysis_results['raw_analysis'][:200] + "..." if len(analysis_results['raw_analysis']) > 200 else analysis_results['raw_analysis']
                            self.log_message(f"Analysis preview: {preview}")
                            
                            # Display the full analysis in the Analysis tab
                            self.analysis_text.delete('1.0', tk.END)
                            self.analysis_text.insert('1.0', analysis_results['raw_analysis'])
                            # Switch to the Analysis tab
                            for i, tab_id in enumerate(self.output_notebook.tabs()):
                                if self.output_notebook.tab(tab_id, "text") == "Analysis":
                                    self.output_notebook.select(i)
                                    break
                    except Exception as e:
                        self.log_message(f"Error analyzing SERP data: {e}")
                        messagebox.showerror("Pipeline Error", f"Failed during LLM analysis: {e}")
                        self.run_button.config(state=tk.NORMAL)  # Re-enable the Run button
                        return # Stop pipeline
                else:
                    self.log_message("Skipping analysis: No SERP data available.")
            else:
                self.log_message("Step 2: Skipped LLM analysis.")


            blog_result = None
            if self.enable_writing_var.get():
                self.log_message(f"Step 3: Writing blog post using {writing_llm_provider}...")
                if analysis_results:
                    try:
                        # Get word count and tone from UI
                        word_count = self.word_count_var.get()
                        tone = self.tone_var.get()
                        
                        self.log_message(f"Using word count: {word_count}, tone: {tone}")
                        
                        # Pass both provider and specific model if available
                        blog_result = blog_writer.write_blog_post(
                            analysis_results=analysis_results,
                            keyword=keyword,
                            llm_type=writing_llm_provider,
                            model_id=writing_llm_model or None, # Pass specific model or None
                            word_count=word_count,
                            tone=tone  # Pass the tone setting
                        )
                        self.log_message("Blog post written successfully.")
                        self.log_message(f"Title: {blog_result['title']}")
                        self.log_message(f"Word count: {blog_result['actual_word_count']}")
                        
                        # Show a preview of the blog content in the log
                        preview = blog_result['content_html'][:200] + "..." if len(blog_result['content_html']) > 200 else blog_result['content_html']
                        self.log_message(f"Content preview: {preview}")
                        
                        # Display the full blog content in the Blog Content tab
                        self.blog_text.delete('1.0', tk.END)
                        self.blog_text.insert('1.0', blog_result['content_html'])
                        # Switch to the Blog Content tab
                        for i, tab_id in enumerate(self.output_notebook.tabs()):
                            if self.output_notebook.tab(tab_id, "text") == "Blog Content":
                                self.output_notebook.select(i)
                                break
                    except Exception as e:
                        self.log_message(f"Error writing blog post: {e}")
                        messagebox.showerror("Pipeline Error", f"Failed during blog writing: {e}")
                        self.run_button.config(state=tk.NORMAL)  # Re-enable the Run button
                        return # Stop pipeline
                else:
                    self.log_message("Skipping blog writing: No analysis results available.")
            else:
                self.log_message("Step 3: Skipped blog writing.")
                
            # Step 4: Critique blog content
            review_result = None
            if self.enable_review_var.get():
                self.log_message(f"Step 4: Reviewing blog post using {critic_llm_provider}...")
                if blog_result and analysis_results and serp_data:
                    try:
                        # Call the blog_critic module
                        review_result = blog_critic.critique_blog_content(
                            serp_data=serp_data,
                            analysis_results=analysis_results,
                            blog_content=blog_result,
                            keyword=keyword,
                            llm_type=critic_llm_provider,
                            model_id=critic_llm_model or None
                        )
                        
                        self.log_message("Blog review completed successfully.")
                        
                        # If there's an overall score, display it
                        if 'overall_score' in review_result:
                            self.log_message(f"Overall Score: {review_result['overall_score']}/100")
                        
                        # Display the review in the Review tab
                        self.review_text.delete('1.0', tk.END)
                        self.review_text.insert('1.0', review_result['raw_critique'])
                        
                        # Switch to the Review tab
                        for i, tab_id in enumerate(self.output_notebook.tabs()):
                            if self.output_notebook.tab(tab_id, "text") == "Review":
                                self.output_notebook.select(i)
                                break
                                
                    except Exception as e:
                        self.log_message(f"Error reviewing blog post: {e}")
                        messagebox.showerror("Pipeline Error", f"Failed during blog review: {e}")
                        # Continue pipeline despite review error
                else:
                    self.log_message("Skipping blog review: Missing required data for review.")
            else:
                self.log_message("Step 4: Skipped blog review.")

            # Step 5: Post to Shopify
            if self.enable_posting_var.get():
                self.log_message("Step 5: Posting to Shopify...")
                if blog_result:
                    try:
                        # Get Shopify credentials from config
                        shop_url = config_manager.get_api_key('shopify_store_url')
                        api_token = config_manager.get_api_key('shopify_api_token')
                        
                        if not shop_url or not api_token or 'YOUR_' in api_token:
                            raise ValueError("Shopify credentials not configured.")
                        
                        # Get additional tags and publish setting from UI
                        additional_tags = self.additional_tags_var.get()
                        publish_immediately = self.publish_immediately_var.get()
                        
                        # Combine keyword with additional tags
                        tags = keyword
                        if additional_tags:
                            tags += ", " + additional_tags
                        
                        self.log_message(f"Publishing as: {'Published' if publish_immediately else 'Draft'}")
                        self.log_message(f"Tags: {tags}")
                        
                        # Post to Shopify
                        article = shopify_poster.post_blog_article(
                            shop_url=shop_url,
                            api_token=api_token,
                            title=blog_result['title'],
                            content_html=blog_result['content_html'],
                            tags=tags,
                            published=publish_immediately
                        )
                        
                        self.log_message(f"Successfully posted to Shopify! Article ID: {article.id}")
                        messagebox.showinfo("Success", f"Blog post successfully created on Shopify!\nTitle: {blog_result['title']}\nStatus: {'Published' if publish_immediately else 'Draft'}")
                        
                    except Exception as e:
                        self.log_message(f"Error posting to Shopify: {e}")
                        messagebox.showerror("Pipeline Error", f"Failed during Shopify posting: {e}")
                        self.run_button.config(state=tk.NORMAL)  # Re-enable the Run button
                        return # Stop pipeline
                else:
                    self.log_message("Skipping Shopify posting: No blog content available.")
            else:
                self.log_message("Step 5: Skipped Shopify posting.")
                
            self.log_message("Pipeline completed successfully!")
            self.status_var.set("Ready - Pipeline completed successfully")
        except Exception as e:
            # General error handling for the pipeline
            self.log_message(f"Unexpected error in pipeline: {e}")
            messagebox.showerror("Pipeline Error", f"An unexpected error occurred: {e}")
        finally:
            # Always re-enable the Run button, even if an error occurred
            self.run_button.config(state=tk.NORMAL)
