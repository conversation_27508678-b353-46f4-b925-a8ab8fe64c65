"""
Planner - The Strategic Brain

This module analyzes the idea queue and decides what to write next based on freshness scoring.
It calculates Final Freshness Scores using the formula:
Final Score = ((TimeScore * 0.5) + (UniquenessScore * 0.5)) * PillarWeight + AngleBonus
"""

import math
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional
import numpy as np
from sentence_transformers import SentenceTransformer

from database import ContentDatabase, deserialize_vector


class ContentPlanner:
    """
    The Strategic Brain that analyzes content ideas and selects what to write next.
    """
    
    def __init__(self):
        """Initialize the planner with database connection."""
        self.db = ContentDatabase()
        self.embedding_model = None
        self._load_embedding_model()
    
    def _load_embedding_model(self):
        """Load the sentence transformer model for similarity calculations."""
        try:
            print("Loading sentence transformer model for planner...")
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            print("Embedding model loaded successfully")
        except Exception as e:
            print(f"Warning: Could not load embedding model: {e}")
            print("Uniqueness scoring will be simplified without embeddings")
            self.embedding_model = None
    
    def calculate_time_score(self, created_date: str, max_age_days: int = 30) -> float:
        """
        Calculate time-based freshness score (0-100).
        Newer ideas get higher scores.
        
        Args:
            created_date: ISO format datetime string
            max_age_days: Maximum age in days for scoring (older = 0 score)
            
        Returns:
            Time score between 0 and 100
        """
        try:
            created = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
            now = datetime.now(created.tzinfo) if created.tzinfo else datetime.now()
            age_days = (now - created).total_seconds() / (24 * 3600)
            
            if age_days >= max_age_days:
                return 0.0
            
            # Linear decay from 100 to 0 over max_age_days
            score = 100 * (1 - age_days / max_age_days)
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            print(f"Error calculating time score: {e}")
            return 50.0  # Default middle score
    
    def calculate_uniqueness_score(self, target_idea: Dict[str, Any], 
                                 all_ideas: List[Dict[str, Any]]) -> float:
        """
        Calculate uniqueness score (0-100) based on similarity to existing ideas.
        More unique ideas get higher scores.
        
        Args:
            target_idea: The idea to score
            all_ideas: All ideas to compare against
            
        Returns:
            Uniqueness score between 0 and 100
        """
        if not self.embedding_model or not target_idea.get('keyword_vector'):
            # Fallback: simple keyword-based uniqueness
            return self._calculate_simple_uniqueness(target_idea, all_ideas)
        
        try:
            target_vector = deserialize_vector(target_idea['keyword_vector'])
            if target_vector is None:
                return self._calculate_simple_uniqueness(target_idea, all_ideas)
            
            similarities = []
            
            for other_idea in all_ideas:
                if other_idea['id'] == target_idea['id']:
                    continue  # Skip self
                
                other_vector = deserialize_vector(other_idea.get('keyword_vector'))
                if other_vector is not None:
                    # Calculate cosine similarity
                    similarity = np.dot(target_vector, other_vector) / (
                        np.linalg.norm(target_vector) * np.linalg.norm(other_vector)
                    )
                    similarities.append(similarity)
            
            if not similarities:
                return 100.0  # Completely unique if no comparisons possible
            
            # Convert similarity to uniqueness (higher similarity = lower uniqueness)
            max_similarity = max(similarities)
            uniqueness_score = 100 * (1 - max_similarity)
            
            return max(0.0, min(100.0, uniqueness_score))
            
        except Exception as e:
            print(f"Error calculating vector-based uniqueness: {e}")
            return self._calculate_simple_uniqueness(target_idea, all_ideas)
    
    def _calculate_simple_uniqueness(self, target_idea: Dict[str, Any], 
                                   all_ideas: List[Dict[str, Any]]) -> float:
        """
        Fallback uniqueness calculation based on keyword similarity.
        
        Args:
            target_idea: The idea to score
            all_ideas: All ideas to compare against
            
        Returns:
            Uniqueness score between 0 and 100
        """
        target_keyword = target_idea['keyword'].lower()
        target_words = set(target_keyword.split())
        
        max_overlap = 0
        
        for other_idea in all_ideas:
            if other_idea['id'] == target_idea['id']:
                continue
            
            other_keyword = other_idea['keyword'].lower()
            other_words = set(other_keyword.split())
            
            if target_words and other_words:
                overlap = len(target_words.intersection(other_words)) / len(target_words.union(other_words))
                max_overlap = max(max_overlap, overlap)
        
        uniqueness_score = 100 * (1 - max_overlap)
        return max(0.0, min(100.0, uniqueness_score))
    
    def calculate_angle_bonus(self, proposed_angle: str) -> float:
        """
        Calculate bonus points based on the quality/specificity of the proposed angle.
        
        Args:
            proposed_angle: The proposed content angle
            
        Returns:
            Bonus points (typically 0-20)
        """
        if not proposed_angle:
            return 0.0
        
        bonus = 0.0
        angle_lower = proposed_angle.lower()
        
        # Bonus for specific, actionable language
        action_words = ['how to', 'guide to', 'step by step', 'complete', 'ultimate', 'comprehensive']
        for word in action_words:
            if word in angle_lower:
                bonus += 3.0
                break
        
        # Bonus for Stuga-aligned terms
        stuga_terms = ['natural', 'sustainable', 'artisan', 'handmade', 'quality', 'traditional', 'crafted']
        for term in stuga_terms:
            if term in angle_lower:
                bonus += 2.0
                break
        
        # Bonus for specificity (longer, more detailed angles)
        if len(proposed_angle) > 100:
            bonus += 5.0
        elif len(proposed_angle) > 50:
            bonus += 2.0
        
        # Bonus for Australian market focus
        if 'australia' in angle_lower or 'aussie' in angle_lower:
            bonus += 3.0
        
        return min(20.0, bonus)  # Cap at 20 points
    
    def calculate_freshness_scores(self, pillar_weights: Dict[str, float], 
                                 freshness_threshold: float = 70.0) -> List[Dict[str, Any]]:
        """
        Calculate freshness scores for all NEW and ON_HOLD content.
        
        Args:
            pillar_weights: Dictionary mapping craft names to weight multipliers
            freshness_threshold: Minimum score threshold for consideration
            
        Returns:
            List of content ideas with calculated scores, sorted by score descending
        """
        # Get all content that needs scoring
        content_to_score = self.db.get_content_by_statuses(['NEW', 'ON_HOLD'])
        
        if not content_to_score:
            print("No content found to score")
            return []
        
        print(f"Calculating freshness scores for {len(content_to_score)} content ideas...")
        
        scored_content = []
        
        for idea in content_to_score:
            try:
                # Calculate component scores
                time_score = self.calculate_time_score(idea['created_date'])
                uniqueness_score = self.calculate_uniqueness_score(idea, content_to_score)
                angle_bonus = self.calculate_angle_bonus(idea.get('proposed_angle', ''))
                
                # Get pillar weight
                craft = idea.get('craft', '')
                pillar_weight = pillar_weights.get(craft, 1.0)
                
                # Calculate final score using the formula
                final_score = ((time_score * 0.5) + (uniqueness_score * 0.5)) * pillar_weight + angle_bonus
                
                # Update the score in the database
                self.db.update_freshness_score(idea['id'], final_score)
                
                # Add to results
                scored_idea = idea.copy()
                scored_idea['freshness_score'] = final_score
                scored_idea['time_score'] = time_score
                scored_idea['uniqueness_score'] = uniqueness_score
                scored_idea['angle_bonus'] = angle_bonus
                scored_idea['pillar_weight'] = pillar_weight
                
                scored_content.append(scored_idea)
                
                print(f"Scored '{idea['keyword']}': Final={final_score:.1f} "
                      f"(Time={time_score:.1f}, Unique={uniqueness_score:.1f}, "
                      f"Weight={pillar_weight:.1f}, Bonus={angle_bonus:.1f})")
                
            except Exception as e:
                print(f"Error scoring idea {idea['id']}: {e}")
                continue
        
        # Sort by freshness score descending
        scored_content.sort(key=lambda x: x['freshness_score'], reverse=True)
        
        print(f"Scoring complete. {len(scored_content)} ideas scored.")
        return scored_content
    
    def select_next_job(self, pillar_weights: Dict[str, float], 
                       freshness_threshold: float = 70.0) -> Optional[Dict[str, Any]]:
        """
        Select the next content idea to work on based on freshness scoring.
        
        Args:
            pillar_weights: Dictionary mapping craft names to weight multipliers
            freshness_threshold: Minimum score threshold for selection
            
        Returns:
            The selected content idea dictionary, or None if no suitable content found
        """
        print(f"Selecting next job with threshold {freshness_threshold}...")
        
        # Calculate scores for all eligible content
        scored_content = self.calculate_freshness_scores(pillar_weights, freshness_threshold)
        
        if not scored_content:
            print("No content available for scoring")
            return None
        
        # Find the highest scoring content that meets the threshold
        for idea in scored_content:
            if idea['freshness_score'] >= freshness_threshold:
                # Mark as PLANNED
                success = self.db.update_content_status(idea['id'], 'PLANNED')
                if success:
                    print(f"Selected job: '{idea['keyword']}' (Score: {idea['freshness_score']:.1f})")
                    return idea
                else:
                    print(f"Failed to update status for idea {idea['id']}, trying next...")
                    continue
        
        print(f"No content meets the freshness threshold of {freshness_threshold}")
        print("Top 3 candidates:")
        for i, idea in enumerate(scored_content[:3]):
            print(f"  {i+1}. '{idea['keyword']}' - Score: {idea['freshness_score']:.1f}")
        
        return None
    
    def get_queue_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current content queue.
        
        Returns:
            Dictionary with queue statistics and top candidates
        """
        stats = self.db.get_content_stats()
        
        # Get top candidates by current freshness score
        all_content = self.db.get_content_by_statuses(['NEW', 'ON_HOLD'])
        top_candidates = sorted(all_content, key=lambda x: x.get('freshness_score', 0), reverse=True)[:5]
        
        return {
            'stats': stats,
            'total_in_queue': stats.get('NEW', 0) + stats.get('ON_HOLD', 0),
            'top_candidates': top_candidates
        }


# Convenience functions
def plan_next_content(pillar_weights: Dict[str, float], 
                     freshness_threshold: float = 70.0) -> Optional[Dict[str, Any]]:
    """
    Convenience function to select the next content to work on.
    
    Args:
        pillar_weights: Dictionary mapping craft names to weight multipliers
        freshness_threshold: Minimum score threshold for selection
        
    Returns:
        The selected content idea dictionary, or None if no suitable content found
    """
    planner = ContentPlanner()
    return planner.select_next_job(pillar_weights, freshness_threshold)


def refresh_all_scores(pillar_weights: Dict[str, float]) -> List[Dict[str, Any]]:
    """
    Convenience function to refresh freshness scores for all content.
    
    Args:
        pillar_weights: Dictionary mapping craft names to weight multipliers
        
    Returns:
        List of all scored content ideas
    """
    planner = ContentPlanner()
    return planner.calculate_freshness_scores(pillar_weights)


# Example usage and testing
if __name__ == '__main__':
    print("Testing Content Planner...")
    
    # Test with sample pillar weights
    test_weights = {
        'grooming': 1.2,
        'leather goods': 1.0,
        'fragrance': 0.8
    }
    
    planner = ContentPlanner()
    
    # Test queue summary
    summary = planner.get_queue_summary()
    print("Queue Summary:", summary)
    
    # Test scoring (requires content in database)
    try:
        scored_content = planner.calculate_freshness_scores(test_weights)
        print(f"\nScored {len(scored_content)} content ideas")
        
        if scored_content:
            print("Top 3 scored ideas:")
            for i, idea in enumerate(scored_content[:3]):
                print(f"  {i+1}. '{idea['keyword']}' - Score: {idea['freshness_score']:.1f}")
        
        # Test job selection
        next_job = planner.select_next_job(test_weights, freshness_threshold=50.0)
        if next_job:
            print(f"\nSelected next job: '{next_job['keyword']}'")
        else:
            print("\nNo job selected (threshold not met or no content available)")
            
    except Exception as e:
        print(f"Could not test scoring: {e}")
        print("Make sure there is content in the database (run idea_generator.py first)")
    
    print("\nContent Planner testing complete!")
