"""
Content Strategist System Test

This script tests the core functionality of the new Content Strategist system
to ensure all components work together properly.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database():
    """Test the database functionality."""
    print("Testing database...")
    try:
        from database import ContentDatabase
        
        db = ContentDatabase()
        
        # Test inserting a sample idea
        content_id = db.insert_content_idea(
            keyword="test shaving brush",
            pillar="shaving brushes", 
            craft="grooming",
            proposed_angle="Test angle for system verification"
        )
        
        print(f"✓ Database test passed - inserted content ID: {content_id}")
        
        # Test retrieving content
        content = db.get_content_by_id(content_id)
        if content:
            print(f"✓ Content retrieval test passed")
        else:
            print("✗ Content retrieval test failed")
            return False
        
        # Test updating status
        success = db.update_content_status(content_id, 'ON_HOLD')
        if success:
            print("✓ Status update test passed")
        else:
            print("✗ Status update test failed")
            return False
        
        # Clean up test data
        db.delete_content(content_id)
        print("✓ Database cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_idea_generator():
    """Test the idea generator functionality."""
    print("\nTesting idea generator...")
    try:
        from idea_generator import IdeaGenerator
        
        generator = IdeaGenerator()
        
        # Test business pillars parsing
        test_pillars = """
        Grooming = shaving brushes, beard oil, natural soap
        Leather Goods = wallets, belts, dopp kits
        """
        
        pillars_dict = generator.parse_business_pillars(test_pillars)
        if pillars_dict and 'Grooming' in pillars_dict:
            print("✓ Business pillars parsing test passed")
        else:
            print("✗ Business pillars parsing test failed")
            return False
        
        # Test creativity vectors
        vectors = generator.get_creativity_vectors()
        if vectors and len(vectors) > 0:
            print(f"✓ Creativity vectors test passed - {len(vectors)} vectors available")
        else:
            print("✗ Creativity vectors test failed")
            return False
        
        print("✓ Idea generator tests passed (LLM generation requires API keys)")
        return True
        
    except Exception as e:
        print(f"✗ Idea generator test failed: {e}")
        return False

def test_planner():
    """Test the planner functionality."""
    print("\nTesting planner...")
    try:
        from planner import ContentPlanner
        from database import ContentDatabase
        
        planner = ContentPlanner()
        db = ContentDatabase()
        
        # Insert test content for planning
        test_ids = []
        for i in range(3):
            content_id = db.insert_content_idea(
                keyword=f"test keyword {i}",
                pillar="test pillar",
                craft="test craft",
                proposed_angle=f"Test angle {i}"
            )
            test_ids.append(content_id)
        
        # Test scoring
        test_weights = {'test craft': 1.0}
        scored_content = planner.calculate_freshness_scores(test_weights)
        
        if scored_content and len(scored_content) >= 3:
            print("✓ Freshness scoring test passed")
        else:
            print("✗ Freshness scoring test failed")
            return False
        
        # Test queue summary
        summary = planner.get_queue_summary()
        if 'stats' in summary and 'total_in_queue' in summary:
            print("✓ Queue summary test passed")
        else:
            print("✗ Queue summary test failed")
            return False
        
        # Clean up test data
        for content_id in test_ids:
            db.delete_content(content_id)
        
        print("✓ Planner tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Planner test failed: {e}")
        return False

def test_worker():
    """Test the worker functionality (basic structure only)."""
    print("\nTesting worker...")
    try:
        from worker import ContentWorker
        
        worker = ContentWorker()
        
        # Test worker initialization
        if hasattr(worker, 'db') and hasattr(worker, 'execute_job'):
            print("✓ Worker initialization test passed")
        else:
            print("✗ Worker initialization test failed")
            return False
        
        print("✓ Worker tests passed (job execution requires API keys and configuration)")
        return True
        
    except Exception as e:
        print(f"✗ Worker test failed: {e}")
        return False

def test_config():
    """Test configuration management."""
    print("\nTesting configuration...")
    try:
        from core import config_manager
        
        # Test loading config
        config = config_manager.load_config()
        if config:
            print("✓ Config loading test passed")
        else:
            print("✗ Config loading test failed")
            return False
        
        # Test getting API keys
        api_keys = config_manager.get_all_api_keys()
        if isinstance(api_keys, dict):
            print("✓ API keys retrieval test passed")
        else:
            print("✗ API keys retrieval test failed")
            return False
        
        print("✓ Configuration tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_gui_imports():
    """Test that GUI components can be imported."""
    print("\nTesting GUI imports...")
    try:
        from gui.main_window import MainWindow
        from gui.settings_window import SettingsWindow
        
        print("✓ GUI import tests passed")
        return True
        
    except Exception as e:
        print(f"✗ GUI import test failed: {e}")
        return False

def main():
    """Run all system tests."""
    print("Content Strategist System Test")
    print("=" * 40)
    
    tests = [
        test_config,
        test_database,
        test_idea_generator,
        test_planner,
        test_worker,
        test_gui_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Content Strategist system is ready to use.")
        print("\nTo start the application, run: python main_gui.py")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("Make sure all required packages are installed: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
