from openai import OpenAI, OpenAIError, RateLimitError, APIConnectionError, AuthenticationError
from ..llm_interface import LLMInterface
import os
from typing import List # Added for type hinting

# Default model if not specified
DEFAULT_OPENAI_MODEL = "gpt-3.5-turbo"

class OpenAILLM(LLMInterface):
    """OpenAI LLM implementation."""

    def __init__(self, api_key: str = None, model_id: str = None, api_endpoint: str = None):
        # api_endpoint is not typically used for standard OpenAI API but could be for Azure etc.
        # We'll prioritize api_key from argument, then environment variable
        resolved_api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not resolved_api_key:
            raise ValueError("OpenAI API key not provided via argument or environment variable.")

        super().__init__(api_key=resolved_api_key, model_id=model_id or DEFAULT_OPENAI_MODEL)
        try:
            self.client = OpenAI(api_key=self.api_key)
        except Exception as e:
            raise ConnectionError(f"Failed to initialize OpenAI client: {e}")

    def generate(self, prompt: str, max_tokens: int = 2048) -> str:
        """Generates text using the OpenAI API."""
        if not self.client:
             raise ConnectionError("OpenAI client not initialized.")

        try:
            response = self.client.chat.completions.create(
                model=self.model_id,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=0.7, # Adjust creativity/factuality
            )
            # Accessing the response content correctly
            if response.choices and response.choices[0].message:
                 content = response.choices[0].message.content
                 return content.strip() if content else ""
            else:
                 raise ValueError("Invalid response structure received from OpenAI.")

        except OpenAIError as e:
            print(f"OpenAI API error: {e}")
            raise Exception(f"OpenAI API error: {e}") from e
        except Exception as e:
            print(f"Error during OpenAI generation: {e}")
            raise Exception(f"Failed to generate text with OpenAI: {e}") from e

    def test_connection(self) -> bool:
        """Tests the connection by making a simple API call."""
        if not self.client:
             raise ConnectionError("OpenAI client not initialized.")
        try:
            # A very lightweight call to test authentication and connectivity
            # Using models.list() is a common way to test API key validity
            response = self.client.models.list()
            
            # Check if we got actual models back
            if response.data and len(response.data) > 0:
                print("OpenAI connection test successful.")
                return True
            else:
                raise ConnectionError("OpenAI returned empty model list.")
                
        except Exception as e:
            print(f"OpenAI connection test failed: {e}")
            
            # Provide a more user-friendly error message
            error_msg = str(e)
            if "Invalid API key" in error_msg or "401" in error_msg:
                raise ConnectionError("Invalid API key provided. Please check your OpenAI API key.")
            elif "429" in error_msg:
                raise ConnectionError("Rate limit exceeded. Please try again later.")
            else:
                raise ConnectionError(f"OpenAI connection failed: {error_msg}")

    def list_models(self) -> List[str]:
        """Fetches a list of available model IDs from OpenAI."""
        if not self.client:
            raise ConnectionError("OpenAI client not initialized.")
        try:
            response = self.client.models.list()
            model_ids = [model.id for model in response.data]
            # Optional: Filter for specific types like 'gpt' if needed
            # model_ids = [id for id in model_ids if 'gpt' in id]
            print(f"Fetched {len(model_ids)} models from OpenAI.")
            return sorted(model_ids) # Return sorted list
        except AuthenticationError:
             raise ConnectionError("Invalid API key provided. Cannot list models.")
        except RateLimitError:
             raise ConnectionError("Rate limit exceeded while trying to list models.")
        except APIConnectionError as e:
             raise ConnectionError(f"Network error connecting to OpenAI to list models: {e}")
        except OpenAIError as e:
            raise Exception(f"OpenAI API error while listing models: {e}") from e
        except Exception as e:
            raise Exception(f"Unexpected error listing OpenAI models: {e}") from e


# Example usage (optional, for testing)
if __name__ == '__main__':
    print("Testing OpenAI LLM Implementation...")
    # Assumes OPENAI_API_KEY is set as an environment variable
    # or create a .env file in the parent directory and load it
    # from dotenv import load_dotenv
    # load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

    try:
        # Test with environment variable key first
        print("\nTesting connection (using env key if set)...")
        openai_llm = OpenAILLM() # Rely on env var
        if openai_llm.test_connection():
            print("\nTesting model listing...")
            try:
                models = openai_llm.list_models()
                print(f"Available models ({len(models)}):")
                # Print first 10 models for brevity
                for model in models[:10]:
                    print(f"- {model}")
                if len(models) > 10:
                    print("...")
            except Exception as e:
                print(f"Failed to list models: {e}")

            print("\nTesting generation...")
            test_prompt = "Explain the concept of SEO in one sentence."
            generated_text = openai_llm.generate(test_prompt, max_tokens=50)
            print(f"Prompt: {test_prompt}")
            print(f"Generated Text: {generated_text}")
        else:
            print("Connection test returned False (should have raised exception on failure).")

    except ValueError as e:
         print(f"Skipping test: {e}") # Likely API key not found
    except ConnectionError as e:
         print(f"Connection test failed: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
