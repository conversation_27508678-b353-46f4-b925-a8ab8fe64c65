import os
from openai import OpenAI, APIConnectionError, AuthenticationError, RateLimitError, APIError
from ..llm_interface import LLMInterface

# Constants
OPENROUTER_API_BASE = "https://openrouter.ai/api/v1"
# Optional: Get site name from env var or config if needed for headers
# OPENROUTER_SITE_NAME = os.getenv("YOUR_SITE_NAME", "SEO Assistant") # Replace with actual app name/URL if required by OpenRouter ToS

class OpenRouterLLM(LLMInterface):
    """LLMInterface implementation for OpenRouter API."""

    def __init__(self, api_key: str, model_id: str = None):
        """
        Initializes the OpenRouter LLM client.

        Args:
            api_key: The OpenRouter API key.
            model_id: The specific OpenRouter model ID to use by default (optional).

        Raises:
            ValueError: If the API key is missing.
        """
        if not api_key:
            raise ValueError("OpenRouter API key is required.")
        super().__init__(api_key=api_key, model_id=model_id)
        try:
            self.client = OpenAI(
                base_url=OPENROUTER_API_BASE,
                api_key=self.api_key,
                # Optional headers if needed
                # default_headers={
                #     "HTTP-Referer": OPENROUTER_SITE_NAME, # Your app name or website
                #     "X-Title": OPENROUTER_SITE_NAME,      # Your app name
                # }
            )
        except Exception as e:
            # Catch potential issues during client initialization
            raise ConnectionError(f"Failed to initialize OpenRouter client: {e}") from e

    def generate(self, prompt: str, model_id: str = None, max_tokens: int = 2048, temperature: float = 0.7) -> str:
        """
        Generates text using the specified OpenRouter model.

        Args:
            prompt: The input prompt.
            model_id: The specific OpenRouter model ID to use (overrides default).
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.

        Returns:
            The generated text.

        Raises:
            ConnectionError: If there's an API connection or authentication issue.
            Exception: For other API errors (e.g., rate limits, invalid requests).
        """
        target_model = model_id or self.model_id
        if not target_model:
            raise ValueError("Model ID must be specified either during initialization or generation.")

        try:
            response = self.client.chat.completions.create(
                model=target_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                # stream=False # Ensure streaming is off for simple generation
            )
            # Ensure response.choices exists and has at least one choice
            if response.choices and len(response.choices) > 0:
                # Check if the message content is not None
                content = response.choices[0].message.content
                if content is not None:
                    return content.strip()
                else:
                    # Handle case where content is None (e.g., function call response)
                    # For now, return empty string or raise error
                    print("Warning: OpenRouter response message content was None.")
                    return "" # Or raise Exception("Generation resulted in None content")
            else:
                raise APIError("Invalid response format from OpenRouter: No choices found.", response=response)

        except AuthenticationError as e:
            raise ConnectionError(f"OpenRouter authentication failed: Invalid API Key? ({e})") from e
        except APIConnectionError as e:
            raise ConnectionError(f"OpenRouter API connection error: {e}") from e
        except RateLimitError as e:
            raise Exception(f"OpenRouter rate limit exceeded: {e}") from e
        except APIError as e: # Catch other OpenAI/API errors
             raise Exception(f"OpenRouter API error: Status={e.status_code}, Message={e.message}") from e
        except Exception as e:
            # Catch-all for unexpected errors
            raise Exception(f"An unexpected error occurred during OpenRouter generation: {e}") from e

    def list_models(self) -> list[str]:
        """
        Fetches the list of available models from OpenRouter.

        Returns:
            A list of model ID strings.

        Raises:
            ConnectionError: If there's an API connection or authentication issue.
            Exception: For other API errors.
        """
        try:
            models_response = self.client.models.list()
            # Ensure models_response.data exists and is iterable
            if models_response and hasattr(models_response, 'data'):
                 # Filter out potential None values or objects without an 'id'
                model_ids = [model.id for model in models_response.data if hasattr(model, 'id') and model.id]
                if not model_ids:
                    print("Warning: OpenRouter models list was empty or malformed.")
                    return []
                return sorted(model_ids) # Return sorted list
            else:
                raise APIError("Invalid response format from OpenRouter models endpoint.", response=models_response)

        except AuthenticationError as e:
            raise ConnectionError(f"OpenRouter authentication failed while listing models: Invalid API Key? ({e})") from e
        except APIConnectionError as e:
            raise ConnectionError(f"OpenRouter API connection error while listing models: {e}") from e
        except APIError as e: # Catch other OpenAI/API errors
             raise Exception(f"OpenRouter API error listing models: Status={e.status_code}, Message={e.message}") from e
        except Exception as e:
            # Catch-all for unexpected errors
            raise Exception(f"An unexpected error occurred listing OpenRouter models: {e}") from e

    def test_connection(self) -> bool:
        """
        Tests the connection by attempting to list models.

        Returns:
            True if listing models succeeds.

        Raises:
            ConnectionError: If the connection or authentication fails.
            Exception: For other errors during the test.
        """
        try:
            # Listing models is a good way to test authentication and basic connectivity
            self.list_models()
            return True
        except (ConnectionError, Exception) as e:
            # Re-raise the specific error encountered during the test
            # The calling function in settings_window handles displaying the error
            raise e
