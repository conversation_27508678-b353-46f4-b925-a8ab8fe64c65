"""
Idea Generator - The Creative Engine

This module generates fresh content ideas by combining business pillars with creativity vectors.
It uses LLMs to generate ideas and sentence-transformers to create vector embeddings.
All generated ideas are stored in the database with status='NEW'.
"""

import random
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import json

from database import ContentDatabase, serialize_vector
from core.llm_interface import get_llm_instance
from core import config_manager


class IdeaGenerator:
    """
    The Creative Engine that generates fresh content ideas.
    """
    
    # Creativity vectors to combine with business pillars
    CREATIVITY_VECTORS = [
        "Beginner's Guide",
        "Advanced Deep Dive", 
        "Common Mistakes",
        "Expert Tips",
        "Buying Guide",
        "Comparison Review",
        "How-To Tutorial",
        "Maintenance & Care",
        "Seasonal Guide",
        "Budget-Friendly Options",
        "Premium Choices",
        "Sustainable Alternatives",
        "Traditional vs Modern",
        "Regional Differences",
        "Historical Perspective",
        "Future Trends",
        "Troubleshooting",
        "Professional vs DIY",
        "Gift Guide",
        "Myths Debunked"
    ]
    
    def __init__(self):
        """Initialize the idea generator with database and embedding model."""
        self.db = ContentDatabase()
        self.embedding_model = None
        self._load_embedding_model()
    
    def _load_embedding_model(self):
        """Load the sentence transformer model for creating embeddings."""
        try:
            print("Loading sentence transformer model...")
            # Using a lightweight but effective model
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            print("Embedding model loaded successfully")
        except Exception as e:
            print(f"Warning: Could not load embedding model: {e}")
            print("Ideas will be generated without vector embeddings")
            self.embedding_model = None
    
    def parse_business_pillars(self, pillars_text: str) -> Dict[str, List[str]]:
        """
        Parse the business pillars text from the GUI settings.
        
        Expected format: "Craft Name = Pillar1, Pillar2, Pillar3"
        
        Args:
            pillars_text: The raw text from the GUI settings
            
        Returns:
            Dictionary mapping craft names to lists of pillars
        """
        pillars_dict = {}
        
        for line in pillars_text.strip().split('\n'):
            line = line.strip()
            if '=' in line:
                craft, pillars = line.split('=', 1)
                craft = craft.strip()
                pillar_list = [p.strip() for p in pillars.split(',') if p.strip()]
                if craft and pillar_list:
                    pillars_dict[craft] = pillar_list
        
        return pillars_dict
    
    def generate_idea_prompt(self, pillar: str, creativity_vector: str, craft: str) -> str:
        """
        Generate a prompt for the LLM to create content ideas.
        
        Args:
            pillar: The business pillar (e.g., "shaving brushes")
            creativity_vector: The creativity approach (e.g., "Beginner's Guide")
            craft: The craft category (e.g., "grooming")
            
        Returns:
            The formatted prompt for the LLM
        """
        return f"""You are a content strategist for Stuga, an Australian maker of high-quality grooming products, perfumes, and leather goods. Stuga focuses on timeless design, natural materials, and sustainable practices.

Your task is to generate 3-5 specific, actionable content ideas that combine the pillar "{pillar}" with the approach "{creativity_vector}" for the craft category "{craft}".

Each idea should:
1. Be specific and actionable (not generic)
2. Align with Stuga's values of quality, craftsmanship, and sustainability
3. Target the Australian market where appropriate
4. Be suitable for blog content that could drive SEO traffic
5. Include a clear target keyword and proposed angle

Format your response as a JSON array with this structure:
[
    {{
        "keyword": "specific target keyword (2-4 words)",
        "proposed_angle": "detailed description of the content approach and unique angle",
        "pillar": "{pillar}",
        "craft": "{craft}",
        "creativity_vector": "{creativity_vector}"
    }}
]

Focus on keywords that people actually search for and angles that provide genuine value to readers interested in quality, artisanal products.

Examples of good keywords: "best badger brush", "natural beard oil benefits", "leather wallet care"
Examples of good angles: "Complete guide to choosing your first quality shaving brush, focusing on natural bristle types and sustainable sourcing", "How to properly maintain handcrafted leather goods to ensure decades of use"

Generate ideas now:"""
    
    def generate_ideas(self, pillars_text: str, target_craft: str = None, 
                      target_creativity_vector: str = None, num_ideas: int = 5) -> List[Dict[str, Any]]:
        """
        Generate new content ideas using LLM and store them in the database.
        
        Args:
            pillars_text: The business pillars text from GUI settings
            target_craft: Optional specific craft to target (if None, random selection)
            target_creativity_vector: Optional specific vector (if None, random selection)
            num_ideas: Number of ideas to generate
            
        Returns:
            List of generated ideas with their database IDs
        """
        # Parse business pillars
        pillars_dict = self.parse_business_pillars(pillars_text)
        if not pillars_dict:
            raise ValueError("No valid business pillars found. Please check the format in settings.")
        
        # Get LLM configuration
        llm_type = config_manager.get_pipeline_setting('analysis_llm') or 'openai'
        api_key = None
        model_id = config_manager.get_pipeline_setting('analysis_llm_model')
        
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_key = config_manager.get_api_key('local_llm_endpoint')
        elif llm_type == 'openrouter':
            api_key = config_manager.get_api_key('openrouter_key')
        elif llm_type == 'groq':
            api_key = config_manager.get_api_key('groq_key')
        
        if not api_key and llm_type != 'local':
            raise ValueError(f"No API key found for LLM type: {llm_type}")
        
        # Get LLM instance
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id)
        
        generated_ideas = []
        ideas_generated = 0
        
        while ideas_generated < num_ideas:
            # Select craft and pillar
            if target_craft and target_craft in pillars_dict:
                craft = target_craft
            else:
                craft = random.choice(list(pillars_dict.keys()))
            
            pillar = random.choice(pillars_dict[craft])
            
            # Select creativity vector
            if target_creativity_vector and target_creativity_vector in self.CREATIVITY_VECTORS:
                creativity_vector = target_creativity_vector
            else:
                creativity_vector = random.choice(self.CREATIVITY_VECTORS)
            
            print(f"Generating ideas for: {craft} > {pillar} + {creativity_vector}")
            
            try:
                # Generate prompt and get LLM response
                prompt = self.generate_idea_prompt(pillar, creativity_vector, craft)
                response = llm.generate(prompt, max_tokens=1000)
                
                # Parse JSON response
                ideas_batch = json.loads(response)
                
                if not isinstance(ideas_batch, list):
                    print("Warning: LLM response was not a list, skipping batch")
                    continue
                
                # Process each idea in the batch
                for idea in ideas_batch:
                    if ideas_generated >= num_ideas:
                        break
                    
                    if not isinstance(idea, dict) or 'keyword' not in idea or 'proposed_angle' not in idea:
                        print(f"Warning: Skipping malformed idea: {idea}")
                        continue
                    
                    keyword = idea['keyword'].strip()
                    proposed_angle = idea['proposed_angle'].strip()
                    
                    if not keyword or not proposed_angle:
                        print("Warning: Skipping idea with empty keyword or angle")
                        continue
                    
                    # Generate vector embedding
                    keyword_vector = None
                    if self.embedding_model:
                        try:
                            embedding = self.embedding_model.encode(keyword)
                            keyword_vector = serialize_vector(embedding)
                        except Exception as e:
                            print(f"Warning: Could not generate embedding for '{keyword}': {e}")
                    
                    # Insert into database
                    content_id = self.db.insert_content_idea(
                        keyword=keyword,
                        pillar=pillar,
                        craft=craft,
                        proposed_angle=proposed_angle,
                        keyword_vector=keyword_vector
                    )
                    
                    generated_ideas.append({
                        'id': content_id,
                        'keyword': keyword,
                        'pillar': pillar,
                        'craft': craft,
                        'proposed_angle': proposed_angle,
                        'creativity_vector': creativity_vector
                    })
                    
                    ideas_generated += 1
                    print(f"Generated idea {ideas_generated}/{num_ideas}: '{keyword}'")
            
            except json.JSONDecodeError as e:
                print(f"Warning: Could not parse LLM response as JSON: {e}")
                print(f"Response was: {response[:200]}...")
                continue
            except Exception as e:
                print(f"Error generating ideas: {e}")
                continue
        
        print(f"Successfully generated {len(generated_ideas)} ideas")
        return generated_ideas
    
    def get_creativity_vectors(self) -> List[str]:
        """Get the list of available creativity vectors."""
        return self.CREATIVITY_VECTORS.copy()


# Convenience function
def generate_content_ideas(pillars_text: str, target_craft: str = None, 
                          target_creativity_vector: str = None, num_ideas: int = 5) -> List[Dict[str, Any]]:
    """
    Convenience function to generate content ideas.
    
    Args:
        pillars_text: The business pillars text from GUI settings
        target_craft: Optional specific craft to target
        target_creativity_vector: Optional specific creativity vector
        num_ideas: Number of ideas to generate
        
    Returns:
        List of generated ideas with their database IDs
    """
    generator = IdeaGenerator()
    return generator.generate_ideas(pillars_text, target_craft, target_creativity_vector, num_ideas)


# Example usage and testing
if __name__ == '__main__':
    print("Testing Idea Generator...")
    
    # Test business pillars parsing
    test_pillars = """
    Grooming = shaving brushes, beard oil, natural soap, aftershave balm
    Leather Goods = wallets, belts, dopp kits, watch straps
    Fragrance = cologne, perfume, essential oils
    """
    
    generator = IdeaGenerator()
    pillars_dict = generator.parse_business_pillars(test_pillars)
    print("Parsed pillars:", pillars_dict)
    
    # Test idea generation (requires LLM configuration)
    try:
        ideas = generator.generate_ideas(test_pillars, num_ideas=3)
        print(f"\nGenerated {len(ideas)} ideas:")
        for idea in ideas:
            print(f"- {idea['keyword']}: {idea['proposed_angle'][:100]}...")
    except Exception as e:
        print(f"Could not test idea generation: {e}")
        print("Make sure LLM configuration is set up in config.ini")
    
    print("\nIdea Generator testing complete!")
