"""
Worker - The Executor

This module executes a single planned job by performing the sequence of tasks:
1. SERP analysis using the salvaged core utilities
2. Blog writing using a master prompt
3. Publishing the draft to the chosen platform (Shopify/WordPress)
4. Updating the job status to PUBLISHED in the database
"""

import re
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from database import ContentDatabase
from core import serp_fetcher, config_manager
from core.llm_interface import get_llm_instance
from core import shopify_poster, wordpress_poster


class ContentWorker:
    """
    The Executor that processes a single planned content job.
    """
    
    def __init__(self):
        """Initialize the worker with database connection."""
        self.db = ContentDatabase()
    
    def execute_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a complete content job from SERP analysis to publication.
        
        Args:
            job: The job dictionary from the planner (content record)
            
        Returns:
            Dictionary with execution results and status
        """
        job_id = job['id']
        keyword = job['keyword']
        
        print(f"Starting job execution for: '{keyword}' (ID: {job_id})")
        
        # Update status to WRITING
        self.db.update_content_status(job_id, 'WRITING')
        
        result = {
            'job_id': job_id,
            'keyword': keyword,
            'success': False,
            'steps_completed': [],
            'errors': [],
            'serp_data': None,
            'analysis_results': None,
            'blog_content': None,
            'platform_url': None
        }
        
        try:
            # Step 1: SERP Analysis
            print("Step 1: Performing SERP analysis...")
            serp_data, analysis_results = self._perform_serp_analysis(keyword)
            result['serp_data'] = serp_data
            result['analysis_results'] = analysis_results
            result['steps_completed'].append('serp_analysis')
            print("✓ SERP analysis completed")
            
            # Step 2: Blog Writing
            print("Step 2: Writing blog content...")
            blog_content, meta_description = self._write_blog_content(
                keyword, analysis_results, job.get('proposed_angle', '')
            )
            result['blog_content'] = blog_content
            result['meta_description'] = meta_description
            result['steps_completed'].append('blog_writing')
            print("✓ Blog content written")
            
            # Step 3: Publishing
            print("Step 3: Publishing content...")
            platform_url = self._publish_content(keyword, blog_content, meta_description, job)
            result['platform_url'] = platform_url
            result['steps_completed'].append('publishing')
            print(f"✓ Content published at: {platform_url}")
            
            # Step 4: Update database
            print("Step 4: Updating database...")
            self.db.update_content_published(job_id, platform_url)
            result['steps_completed'].append('database_update')
            print("✓ Database updated")
            
            result['success'] = True
            print(f"Job '{keyword}' completed successfully!")
            
        except Exception as e:
            error_msg = f"Job execution failed: {str(e)}"
            print(f"✗ {error_msg}")
            result['errors'].append(error_msg)
            
            # Update status back to ON_HOLD on failure
            self.db.update_content_status(job_id, 'ON_HOLD')
        
        return result
    
    def _perform_serp_analysis(self, keyword: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Perform SERP data fetching and analysis.
        
        Args:
            keyword: The target keyword
            
        Returns:
            Tuple of (serp_data, analysis_results)
        """
        # Get SERP API configuration
        serp_api_key = config_manager.get_api_key('serpapi_key')
        if not serp_api_key:
            raise ValueError("SERP API key not found in configuration")
        
        # Get SERP settings
        serp_type = config_manager.get_pipeline_setting('serp_type') or 'Combined (Search + Autocomplete)'
        country_code = config_manager.get_pipeline_setting('serp_country_code') or 'us'
        language_code = config_manager.get_pipeline_setting('serp_language_code') or 'en'
        max_calls = config_manager.get_pipeline_setting('serp_api_max_calls') or 1
        
        # Fetch SERP data
        if 'Combined' in serp_type:
            serp_data = serp_fetcher.fetch_combined_data(
                query=keyword,
                api_key=serp_api_key,
                gl=country_code,
                hl=language_code,
                max_calls=max_calls
            )
        else:
            serp_data = serp_fetcher.fetch_serp_data(
                query=keyword,
                api_key=serp_api_key,
                gl=country_code,
                hl=language_code,
                max_calls=max_calls
            )
        
        # Analyze SERP data using LLM
        analysis_results = self._analyze_serp_data(serp_data, keyword)
        
        return serp_data, analysis_results
    
    def _analyze_serp_data(self, serp_data: Dict[str, Any], keyword: str) -> Dict[str, Any]:
        """
        Analyze SERP data using LLM.
        
        Args:
            serp_data: The SERP data from SerpAPI
            keyword: The target keyword
            
        Returns:
            Analysis results dictionary
        """
        # Get LLM configuration for analysis
        llm_type = config_manager.get_pipeline_setting('analysis_llm') or 'openai'
        model_id = config_manager.get_pipeline_setting('analysis_llm_model')
        
        # Get API key
        api_key = None
        api_endpoint = None
        
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        elif llm_type == 'openrouter':
            api_key = config_manager.get_api_key('openrouter_key')
        elif llm_type == 'groq':
            api_key = config_manager.get_api_key('groq_key')
        
        # Get analysis prompt
        analysis_prompt_template = config_manager.get_prompt('analysis_prompt')
        if not analysis_prompt_template:
            raise ValueError("Analysis prompt not found in configuration")
        
        # Format the prompt
        prompt = analysis_prompt_template.format(
            keyword=keyword,
            serp_data=serp_data
        )
        
        # Get LLM instance and generate analysis
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id, api_endpoint=api_endpoint)
        analysis_text = llm.generate(prompt, max_tokens=1500)
        
        return {
            'raw_analysis': analysis_text,
            'keyword': keyword,
            'llm_type': llm_type,
            'model_id': model_id
        }
    
    def _write_blog_content(self, keyword: str, analysis_results: Dict[str, Any], 
                           proposed_angle: str) -> Tuple[str, str]:
        """
        Write blog content using LLM.
        
        Args:
            keyword: The target keyword
            analysis_results: The SERP analysis results
            proposed_angle: The proposed content angle
            
        Returns:
            Tuple of (blog_content_html, meta_description)
        """
        # Get LLM configuration for writing
        llm_type = config_manager.get_pipeline_setting('writing_llm') or 'openai'
        model_id = config_manager.get_pipeline_setting('writing_llm_model')
        
        # Get API key
        api_key = None
        api_endpoint = None
        
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        elif llm_type == 'openrouter':
            api_key = config_manager.get_api_key('openrouter_key')
        elif llm_type == 'groq':
            api_key = config_manager.get_api_key('groq_key')
        
        # Get writing settings
        word_count = config_manager.get_pipeline_setting('blog_word_count') or 1000
        tone = config_manager.get_pipeline_setting('blog_tone') or 'Professional'
        
        # Get writing prompt
        writing_prompt_template = config_manager.get_prompt('writing_prompt')
        if not writing_prompt_template:
            raise ValueError("Writing prompt not found in configuration")
        
        # Format the prompt
        prompt = writing_prompt_template.format(
            keyword=keyword,
            analysis_results=analysis_results['raw_analysis'],
            word_count=word_count,
            gatekeeper_feedback=""  # No gatekeeper in new architecture
        )
        
        # Get LLM instance and generate content
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id, api_endpoint=api_endpoint)
        response = llm.generate(prompt, max_tokens=2500)
        
        # Extract meta description and content
        meta_description = ""
        blog_content = response
        
        # Look for META_DESCRIPTION: at the start
        if response.startswith("META_DESCRIPTION:"):
            lines = response.split('\n', 1)
            if len(lines) >= 2:
                meta_description = lines[0].replace("META_DESCRIPTION:", "").strip()
                blog_content = lines[1].strip()
        
        return blog_content, meta_description
    
    def _publish_content(self, keyword: str, blog_content: str, meta_description: str, 
                        job: Dict[str, Any]) -> str:
        """
        Publish content to the configured platform.
        
        Args:
            keyword: The target keyword (used as title base)
            blog_content: The HTML blog content
            meta_description: The meta description
            job: The job dictionary with additional context
            
        Returns:
            The URL where the content was published
        """
        # Determine which platform to publish to
        publish_shopify = config_manager.get_pipeline_setting('enable_shopify_post')
        
        # Generate title from keyword and proposed angle
        title = self._generate_title(keyword, job.get('proposed_angle', ''))
        
        # Generate tags
        tags = self._generate_tags(job)
        
        if publish_shopify:
            return self._publish_to_shopify(title, blog_content, meta_description, tags)
        else:
            return self._publish_to_wordpress(title, blog_content, meta_description, tags)
    
    def _generate_title(self, keyword: str, proposed_angle: str) -> str:
        """Generate a title from keyword and proposed angle."""
        # Simple title generation - could be enhanced with LLM
        title_words = keyword.split()
        title = ' '.join(word.capitalize() for word in title_words)
        
        # Add context from angle if available
        if proposed_angle and len(proposed_angle) > 20:
            # Extract key phrases from angle
            if 'guide' in proposed_angle.lower():
                title = f"The Complete Guide to {title}"
            elif 'how to' in proposed_angle.lower():
                title = f"How to Choose the Best {title}"
            elif 'best' in proposed_angle.lower():
                title = f"Best {title} for Quality and Value"
            else:
                title = f"Everything You Need to Know About {title}"
        
        return title
    
    def _generate_tags(self, job: Dict[str, Any]) -> str:
        """Generate tags for the content."""
        tags = []
        
        # Add pillar and craft as tags
        if job.get('pillar'):
            tags.append(job['pillar'])
        if job.get('craft'):
            tags.append(job['craft'])
        
        # Add some default Stuga tags
        tags.extend(['SEO', 'Quality', 'Australian Made'])
        
        # Add additional tags from config
        additional_tags = config_manager.get_pipeline_setting('shopify_additional_tags')
        if additional_tags:
            tags.extend([tag.strip() for tag in additional_tags.split(',') if tag.strip()])
        
        return ', '.join(tags)
    
    def _publish_to_shopify(self, title: str, content: str, meta_description: str, tags: str) -> str:
        """Publish content to Shopify."""
        shop_url = config_manager.get_setting('SHOPIFY', 'shop_url')
        api_token = config_manager.get_setting('SHOPIFY', 'api_token')
        
        if not shop_url or not api_token:
            raise ValueError("Shopify credentials not configured")
        
        # Determine if should publish immediately
        publish_immediately = config_manager.get_pipeline_setting('shopify_publish_immediately')
        
        # Check if should upload meta description
        upload_meta = config_manager.get_setting('SHOPIFY', 'upload_meta_description')
        meta_to_upload = meta_description if upload_meta else None
        
        article = shopify_poster.post_blog_article(
            shop_url=shop_url,
            api_token=api_token,
            title=title,
            content_html=content,
            author="Stuga Content Team",
            tags=tags,
            published=publish_immediately,
            meta_description=meta_to_upload
        )
        
        # Construct the URL (this is a simplified version)
        shop_domain = shop_url.replace('https://', '').replace('/', '')
        return f"https://{shop_domain}/blogs/news/{article.id}"
    
    def _publish_to_wordpress(self, title: str, content: str, meta_description: str, tags: str) -> str:
        """Publish content to WordPress."""
        site_url = config_manager.get_setting('WORDPRESS', 'site_url')
        username = config_manager.get_setting('WORDPRESS', 'username')
        
        if not site_url or not username:
            raise ValueError("WordPress credentials not configured")
        
        # This would need to be implemented based on wordpress_poster.py
        # For now, return a placeholder
        return f"{site_url}/blog/draft-post"


# Convenience function
def execute_content_job(job: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function to execute a content job.
    
    Args:
        job: The job dictionary from the planner
        
    Returns:
        Dictionary with execution results
    """
    worker = ContentWorker()
    return worker.execute_job(job)


# Example usage and testing
if __name__ == '__main__':
    print("Testing Content Worker...")
    
    # This would typically be called with a job from the planner
    # For testing, we'll create a mock job
    mock_job = {
        'id': 1,
        'keyword': 'best shaving brush',
        'pillar': 'shaving brushes',
        'craft': 'grooming',
        'proposed_angle': 'Complete guide to choosing quality shaving brushes for beginners',
        'status': 'PLANNED'
    }
    
    try:
        worker = ContentWorker()
        result = worker.execute_job(mock_job)
        
        print(f"Job execution result:")
        print(f"Success: {result['success']}")
        print(f"Steps completed: {result['steps_completed']}")
        if result['errors']:
            print(f"Errors: {result['errors']}")
        if result['platform_url']:
            print(f"Published at: {result['platform_url']}")
            
    except Exception as e:
        print(f"Could not test worker: {e}")
        print("Make sure all API keys and settings are configured in config.ini")
    
    print("\nContent Worker testing complete!")
