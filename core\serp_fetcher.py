from serpapi import GoogleSearch
import json
from typing import Dict, List, Any, Optional, Union

def fetch_serp_data(query: str, api_key: str, location: str = None, hl: str = 'en', gl: str = 'us', num: int = 10, max_calls: int = None) -> dict:
    """
    Fetches search results from SerpApi for a given query.

    Args:
        query: The search query string.
        api_key: The SerpApi API key.
        location: Optional location string (e.g., "Austin, Texas, United States").
        hl: Optional language code (e.g., 'en').
        gl: Optional country code (e.g., 'us').
        num: Optional number of results (default 10).
        max_calls: Optional limit on API calls to make (for usage control).

    Returns:
        A dictionary containing the parsed JSON response from SerpApi.

    Raises:
        Exception: If the API call fails or returns an error status.
    """
    params = {
        "q": query,
        "api_key": api_key,
        "hl": hl,
        "gl": gl,
        "num": num,
        "engine": "google", # Explicitly set engine
        # "location": location, # Add location if provided
    }
    if location:
        params["location"] = location

    print(f"SerpApi Params: {params}") # Debug print
    
    # Add a warning about possible API calls if max_calls is set
    if max_calls is not None:
        print(f"SERP API call limit set to: {max_calls}")

    try:
        search = GoogleSearch(params)
        results = search.get_dict()

        # Check for errors in the response
        if 'error' in results:
            raise Exception(f"SerpApi Error: {results['error']}")
        if results.get('search_metadata', {}).get('status') == 'Error':
             raise Exception(f"SerpApi Search Error: {results.get('search_metadata', {}).get('error_message', 'Unknown error')}")

        # Basic check if results seem valid (e.g., organic_results exist)
        if 'organic_results' not in results:
             print("Warning: 'organic_results' not found in SerpApi response.") # Or raise error?

        return results

    except Exception as e:
        print(f"Error during SerpApi call: {e}")
        # Re-raise the exception so the GUI can catch it
        raise Exception(f"SerpApi request failed: {e}")


def fetch_autocomplete_data(query: str, api_key: str, hl: str = 'en', gl: str = 'us') -> Dict[str, Any]:
    """
    Fetches autocomplete suggestions from SerpApi Google Autocomplete API.
    
    Args:
        query: The search query string to get suggestions for.
        api_key: The SerpApi API key.
        hl: Optional language code (e.g., 'en').
        gl: Optional country code (e.g., 'us').
    
    Returns:
        A dictionary containing the parsed JSON response from SerpApi with autocomplete suggestions.
    
    Raises:
        Exception: If the API call fails or returns an error status.
    """
    params = {
        "q": query,
        "api_key": api_key,
        "hl": hl,
        "gl": gl,
        "engine": "google_autocomplete"
    }
    
    print(f"SerpApi Autocomplete Params: {params}") # Debug print
    
    try:
        search = GoogleSearch(params)
        results = search.get_dict()
        
        # Check for errors in the response
        if 'error' in results:
            raise Exception(f"SerpApi Error: {results['error']}")
        if results.get('search_metadata', {}).get('status') == 'Error':
            raise Exception(f"SerpApi Search Error: {results.get('search_metadata', {}).get('error_message', 'Unknown error')}")
        
        # Basic check if results seem valid
        if 'suggestions' not in results:
            print("Warning: 'suggestions' not found in SerpApi response.") # Or raise error?
        
        return results
        
    except Exception as e:
        print(f"Error during SerpApi Autocomplete call: {e}")
        # Re-raise the exception so the GUI can catch it
        raise Exception(f"SerpApi Autocomplete request failed: {e}")


def test_connection(api_key: str) -> bool:
    """
    Tests the connection to SerpApi with a simple query.

    Args:
        api_key: The SerpApi API key to test.

    Returns:
        True if the connection is successful, False otherwise.

    Raises:
        Exception: If the API call fails.
    """
    try:
        # Use a very simple, common query for testing
        # We'll use a single result to minimize API usage
        params = {
            "q": "test",
            "api_key": api_key,
            "num": 1,
            "engine": "google"
        }
        
        search = GoogleSearch(params)
        results = search.get_dict()
        
        # Check if we got a valid response
        if 'error' in results:
            raise Exception(f"SerpApi Error: {results['error']}")
        if results.get('search_metadata', {}).get('status') == 'Error':
            raise Exception(f"SerpApi Search Error: {results.get('search_metadata', {}).get('error_message', 'Unknown error')}")
            
        # Check for organic_results to verify we got actual data back
        if 'organic_results' not in results:
            raise Exception("SERP API call did not return expected data structure.")
            
        return True
    except Exception as e:
        # Let the caller handle the exception details if needed
        print(f"SerpApi connection test failed: {e}")
        raise e # Re-raise to indicate failure reason


def fetch_combined_data(query: str, api_key: str, hl: str = 'en', gl: str = 'us', num: int = 10, max_calls: int = None) -> dict:
    """
    Fetches both Google Search and Google Autocomplete data and combines them into a single result.
    
    Args:
        query: The search query string.
        api_key: The SerpApi API key.
        hl: Optional language code (e.g., 'en').
        gl: Optional country code (e.g., 'us').
        num: Optional number of results for search (default 10).
        max_calls: Optional limit on API calls to make (for usage control).
    
    Returns:
        A dictionary containing both search results and autocomplete suggestions.
    
    Raises:
        Exception: If either API call fails.
    """
    # Fetch both types of data
    search_data = fetch_serp_data(query, api_key, hl=hl, gl=gl, num=num, max_calls=max_calls)
    autocomplete_data = fetch_autocomplete_data(query, api_key, hl=hl, gl=gl)
    
    # Combine the results
    combined_data = search_data.copy()  # Start with the search data
    
    # Add autocomplete suggestions
    if 'suggestions' in autocomplete_data:
        combined_data['autocomplete_suggestions'] = autocomplete_data['suggestions']
    
    # Add metadata about the combined request
    if 'search_metadata' in combined_data:
        combined_data['search_metadata']['combined_request'] = True
    
    return combined_data

# Example usage (optional, for testing)
if __name__ == '__main__':
    import os
    from dotenv import load_dotenv
    # Load API key from a .env file in the parent directory for testing
    load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))
    test_api_key = os.getenv('SERPAPI_API_KEY') # Assumes you have a .env file for testing

    if test_api_key and 'YOUR_' not in test_api_key:
        print("Testing SerpApi Connection...")
        try:
            if test_connection(test_api_key):
                print("Connection Successful!")
                print("\nFetching results for 'best shaving brushes'...")
                data = fetch_serp_data("best shaving brushes", test_api_key, gl='au') # Example: Australia
                print("\n--- Sample Results ---")
                print(f"Search Info: {data.get('search_information', {}).get('query_displayed', 'N/A')}")
                print(f"Organic Results Count: {len(data.get('organic_results', []))}")
                if data.get('organic_results'):
                    print(f"First Result Title: {data['organic_results'][0].get('title')}")
                print(f"Related Questions Count: {len(data.get('related_questions', []))}")
                print(f"Related Searches Count: {len(data.get('related_searches', []))}")
                # print("\nFull Data (first 500 chars):")
                # print(json.dumps(data, indent=2)[:500] + "...")

                print("\nTesting Autocomplete API...")
                autocomplete_data = fetch_autocomplete_data("best shaving", test_api_key)
                print("\n--- Sample Autocomplete Results ---")
                if autocomplete_data.get('suggestions'):
                    print(f"Suggestions Count: {len(autocomplete_data['suggestions'])}")
                    print("Suggestions:")
                    for suggestion in autocomplete_data['suggestions']:
                        print(f"  - {suggestion.get('value', 'N/A')}")
                else:
                    print("No suggestions found.")
            else:
                # Should not happen if test_connection raises exception on failure
                print("Connection Test returned False (unexpected).")
        except Exception as e:
            print(f"Test failed: {e}")
    else:
        print("SERPAPI_API_KEY not found in .env or is a placeholder. Skipping live test.")
        print("Create a .env file in the seo_assistant directory with your key for testing:")
        print("SERPAPI_API_KEY=your_actual_key")
