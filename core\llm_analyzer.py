import json
from .llm_interface import get_llm_instance
from . import config_manager
from typing import Dict, Any, Optional # Added Optional (though not strictly needed as model_id already had type hint)

def analyze_serp_data(serp_data: dict, keyword: str, llm_type: str = None, api_key: str = None, model_id: Optional[str] = None, api_endpoint: str = None) -> dict:
    """
    Analyzes SERP data using an LLM to extract keywords, questions, and insights.

    Args:
        serp_data: The SERP API response data (dictionary).
        keyword: The original search keyword.
        llm_type: The type of LLM to use ('openai', 'gemini', 'anthropic', 'local').
        api_key: Optional API key (if not provided, will use config).
        model_id: Optional specific model ID to use (overrides default/config).
        api_endpoint: Optional API endpoint for local LLMs.

    Returns:
        A dictionary containing the analysis results.

    Raises:
        Exception: If the analysis fails.
    """
    # If LLM type not specified, get from config
    if not llm_type:
        llm_type = config_manager.get_pipeline_setting('analysis_llm')
        if not llm_type:
            raise ValueError("No LLM type specified for analysis and none found in config.")

    # If API key not provided, get from config based on LLM type
    if not api_key:
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        # API key validation will be done by the LLM implementation

    # Get the prompt template from config
    prompt_template = config_manager.get_prompt('analysis_prompt')
    if not prompt_template:
        # Fallback to default prompt if not found in config
        prompt_template = """
        Analyze the following SERP data for "{keyword}". Extract the following:
        
        1. Top 5 relevant short-tail keywords (1-2 words)
        2. Top 5 long-tail keywords (3+ words)
        3. Top 5 People Also Ask questions
        4. Main search intent (informational, transactional, navigational)
        5. Emerging trends or themes
        
        Present the results in a structured format. SERP Data: {serp_data}
        """

    # Extract relevant parts of SERP data to simplify the prompt
    simplified_data = simplify_serp_data(serp_data)
    
    # Format the prompt with the keyword and simplified SERP data
    prompt = prompt_template.format(
        keyword=keyword,
        serp_data=json.dumps(simplified_data, indent=2)
    )

    # Get LLM instance and generate analysis
    try:
        # Determine the final model_id to use:
        # Priority: 1. model_id passed to function, 2. config for local, 3. default in LLM class
        final_model_id = model_id # Use function argument if provided
        if not final_model_id and llm_type == 'local':
             # Fallback to config only for local LLM if no specific model was passed
             final_model_id = config_manager.get_api_key('local_llm_model') or None

        llm = get_llm_instance(
            llm_type=llm_type,
            api_key=api_key,
            model_id=final_model_id, # Pass the determined model_id
            api_endpoint=api_endpoint
        )

        print(f"Analyzing SERP data using {llm_type} (Model: {llm.model_id})...") # Log the actual model used
        analysis_text = llm.generate(prompt)
        
        # Parse the analysis text into a structured format
        # This is a simple approach - in a real implementation, you might want to
        # have the LLM return JSON directly or use more robust parsing
        analysis_results = {
            'raw_analysis': analysis_text,
            'keyword': keyword,
            'llm_type': llm_type,
            # Additional structured fields could be extracted here
        }
        
        return analysis_results
        
    except Exception as e:
        print(f"Error during SERP analysis: {e}")
        raise Exception(f"Failed to analyze SERP data: {e}")


def simplify_serp_data(serp_data: dict) -> dict:
    """
    Extracts and simplifies the most relevant parts of the SERP data for analysis.
    
    Args:
        serp_data: The full SERP API response.
        
    Returns:
        A simplified dictionary with the most relevant parts.
    """
    simplified = {}
    
    # Extract organic results (titles, snippets, links)
    if 'organic_results' in serp_data:
        simplified['organic_results'] = []
        for result in serp_data['organic_results'][:10]:  # Limit to top 10
            simplified['organic_results'].append({
                'title': result.get('title', ''),
                'snippet': result.get('snippet', ''),
                'link': result.get('link', '')
            })
    
    # Extract "People Also Ask" questions
    if 'related_questions' in serp_data:
        simplified['related_questions'] = []
        for question in serp_data['related_questions']:
            simplified['related_questions'].append({
                'question': question.get('question', ''),
                'snippet': question.get('snippet', '')
            })
    
    # Extract related searches
    if 'related_searches' in serp_data:
        simplified['related_searches'] = []
        for search in serp_data['related_searches']:
            simplified['related_searches'].append(search.get('query', ''))
    
    # Extract knowledge graph if present
    if 'knowledge_graph' in serp_data:
        kg = serp_data['knowledge_graph']
        simplified['knowledge_graph'] = {
            'title': kg.get('title', ''),
            'type': kg.get('type', ''),
            'description': kg.get('description', '')
        }
    
    return simplified


# Example usage (optional, for testing)
if __name__ == '__main__':
    import os
    from dotenv import load_dotenv
    from .serp_fetcher import fetch_serp_data
    
    # Load API keys from .env file
    load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))
    
    # Test with a sample keyword
    test_keyword = "australian made shaving brushes"
    serp_api_key = os.getenv('SERPAPI_API_KEY')
    openai_api_key = os.getenv('OPENAI_API_KEY')
    
    if serp_api_key and openai_api_key:
        try:
            print(f"Fetching SERP data for '{test_keyword}'...")
            serp_data = fetch_serp_data(test_keyword, serp_api_key)
            
            print("Analyzing SERP data with OpenAI...")
            # Example specifying a model (optional)
            # analysis_model = "gpt-4" # Or another specific model ID
            analysis = analyze_serp_data(
                serp_data=serp_data,
                keyword=test_keyword,
                llm_type='openai',
                api_key=openai_api_key,
                # model_id=analysis_model # Uncomment to test specific model
            )

            print("\n--- Analysis Results ---")
            print(analysis['raw_analysis'])
            
        except Exception as e:
            print(f"Test failed: {e}")
    else:
        print("API keys not found in .env file. Skipping test.")
