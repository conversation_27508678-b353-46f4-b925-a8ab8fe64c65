import configparser
import os

CONFIG_FILE = os.path.join(os.path.dirname(__file__), '..', 'config.ini') # Path relative to this file

def load_config():
    """Loads the configuration from config.ini."""
    config = configparser.ConfigParser()
    # Preserve case for keys
    config.optionxform = str
    if not os.path.exists(CONFIG_FILE):
        # Handle case where config file might be missing (though we just created it)
        # You could create a default one here or raise an error
        print(f"Warning: Config file not found at {CONFIG_FILE}")
        return config # Return empty config
    config.read(CONFIG_FILE)
    return config

def save_config(config):
    """Saves the configuration object back to config.ini."""
    with open(CONFIG_FILE, 'w') as configfile:
        config.write(configfile)

def get_api_key(key_name):
    """Gets a specific API key."""
    config = load_config()
    return config.get('API_KEYS', key_name, fallback=None)

def get_all_api_keys():
    """Gets all API keys as a dictionary."""
    config = load_config()
    if 'API_KEYS' in config:
        return dict(config['API_KEYS'])
    return {}

def get_pipeline_setting(setting_name):
    """Gets a specific pipeline setting (gating flags or LLM choice)."""
    config = load_config()
    if setting_name.startswith('enable_'):
        # Return boolean for enable flags
        return config.getboolean('PIPELINE_SETTINGS', setting_name, fallback=True)
    else:
        # Return string for LLM choices
        return config.get('PIPELINE_SETTINGS', setting_name, fallback=None)

def get_all_pipeline_settings():
    """Gets all pipeline settings as a dictionary."""
    config = load_config()
    settings = {}
    if 'PIPELINE_SETTINGS' in config:
        for key, value in config['PIPELINE_SETTINGS'].items():
            if key.startswith('enable_'):
                settings[key] = config.getboolean('PIPELINE_SETTINGS', key)
            else:
                settings[key] = value
    return settings

def get_prompt(prompt_name):
    """Gets a specific prompt template."""
    config = load_config()
    return config.get('PROMPTS', prompt_name, fallback=None)

def get_all_prompts():
    """Gets all prompts as a dictionary."""
    config = load_config()
    if 'PROMPTS' in config:
        return dict(config['PROMPTS'])
    return {}

def update_setting(section, key, value):
    """Updates a single setting and saves the config file."""
    config = load_config()
    if not config.has_section(section):
        config.add_section(section)
    config.set(section, key, str(value)) # Ensure value is string for saving
    save_config(config)

def update_multiple_settings(settings_dict):
    """Updates multiple settings from a dictionary {'section.key': value}."""
    config = load_config()
    for key_path, value in settings_dict.items():
        section, key = key_path.split('.', 1)
        if not config.has_section(section):
            config.add_section(section)
            
        # Handle the case where value is None (to remove an entry)
        if value is None:
            if section in config and key in config[section]:
                config.remove_option(section, key)
        else:
            config.set(section, key, str(value))
    save_config(config)

# New functions for model lists
def get_setting(section, key, default=None):
    """Gets a setting from any section with an optional default value."""
    config = load_config()
    if section in config and key in config[section]:
        return config[section][key]
    return default

def set_setting(section, key, value):
    """Sets a single setting in any section."""
    config = load_config()
    if not config.has_section(section):
        config.add_section(section)
    config.set(section, key, str(value))
    save_config(config)

def get_saved_models(provider):
    """Gets saved models for a specific provider."""
    config = load_config()
    if 'MODEL_LISTS' in config and provider in config['MODEL_LISTS']:
        # Convert the comma-separated string back to a list
        models_str = config.get('MODEL_LISTS', provider)
        return models_str.split(',') if models_str else []
    return []

def get_all_saved_models():
    """Gets all saved model lists as a dictionary."""
    config = load_config()
    saved_models = {}
    if 'MODEL_LISTS' in config:
        for provider, models_str in config['MODEL_LISTS'].items():
            saved_models[provider] = models_str.split(',') if models_str else []
    return saved_models

def save_models_list(provider, models):
    """Saves a list of models for a provider."""
    config = load_config()
    if not config.has_section('MODEL_LISTS'):
        config.add_section('MODEL_LISTS')
    # Join the list into a comma-separated string
    config.set('MODEL_LISTS', provider, ','.join(models))
    save_config(config)

# Example usage (optional, for testing)
if __name__ == '__main__':
    print("Loading config...")
    conf = load_config()
    print("Sections:", conf.sections())

    print("\nAPI Keys:")
    print(get_all_api_keys())

    print("\nPipeline Settings:")
    print(get_all_pipeline_settings())
    print("SERP Fetch Enabled:", get_pipeline_setting('enable_serp_fetch'))
    print("Analysis LLM:", get_pipeline_setting('analysis_llm'))


    print("\nPrompts:")
    print(get_all_prompts())
    print("Analysis Prompt:", get_prompt('analysis_prompt'))

    # Example update
    # print("\nUpdating analysis LLM to gemini...")
    # update_setting('PIPELINE_SETTINGS', 'analysis_llm', 'gemini')
    # print("New Analysis LLM:", get_pipeline_setting('analysis_llm'))
    # print("Reverting...")
    # update_setting('PIPELINE_SETTINGS', 'analysis_llm', 'local') # Revert for consistency
