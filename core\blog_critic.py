import json
from .llm_interface import get_llm_instance
from . import config_manager
from typing import Dict, Any, Optional

def critique_blog_content(
    serp_data: dict, 
    analysis_results: dict,
    blog_content: dict,
    keyword: str, 
    llm_type: str = None, 
    api_key: str = None, 
    model_id: Optional[str] = None, 
    api_endpoint: str = None
) -> dict:
    """
    Evaluates blog content against SERP data and analysis to provide quality assessment and recommendations.

    Args:
        serp_data: The SERP API response data (dictionary).
        analysis_results: The output from the analysis LLM.
        blog_content: The generated blog post content and metadata.
        keyword: The original search keyword.
        llm_type: The type of LLM to use ('openai', 'gemini', 'anthropic', 'local').
        api_key: Optional API key (if not provided, will use config).
        model_id: Optional specific model ID to use (overrides default/config).
        api_endpoint: Optional API endpoint for local LLMs.

    Returns:
        A dictionary containing the critique results.

    Raises:
        Exception: If the critique fails.
    """
    # If LLM type not specified, get from config
    if not llm_type:
        llm_type = config_manager.get_pipeline_setting('critic_llm')
        if not llm_type:
            raise ValueError("No LLM type specified for critic and none found in config.")

    # If API key not provided, get from config based on LLM type
    if not api_key:
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        # API key validation will be done by the LLM implementation

    # Get the prompt template from config
    prompt_template = config_manager.get_prompt('critic_prompt')
    if not prompt_template:
        # Fallback to default prompt if not found in config
        prompt_template = """
        Critique the following blog post for SEO effectiveness based on the source SERP data and analysis.
        
        Original Keyword: "{keyword}"
        Target Word Count: {target_word_count}
        Actual Word Count: {actual_word_count}
        Specified Tone: {tone}
        
        Analyze the following aspects:
        1. Keyword usage (primary and secondary keywords)
        2. Content structure and readability
        3. Alignment with search intent
        4. Coverage of "People Also Ask" questions
        5. Overall SEO optimization
        
        SERP Data: {serp_data}
        
        Analysis Results: {analysis_results}
        
        Blog Content: {blog_content}
        
        Provide a structured critique with:
        - Overall score (0-100)
        - Scores for each category (0-100)
        - Specific recommendations for improvement
        - Key strengths and weaknesses
        
        Format your response as a detailed report with clear sections.
        """

    # Prepare data for prompt variables
    analysis_text = analysis_results.get('raw_analysis', json.dumps(analysis_results, indent=2))
    blog_text = blog_content.get('content_html', '') if isinstance(blog_content, dict) else blog_content
    target_word_count = blog_content.get('target_word_count', 0) if isinstance(blog_content, dict) else 0
    actual_word_count = blog_content.get('actual_word_count', 0) if isinstance(blog_content, dict) else len(blog_text.split())
    tone = blog_content.get('tone', 'Not specified') if isinstance(blog_content, dict) else 'Not specified'
    
    # Simplify SERP data to focus on key elements
    simplified_serp = simplify_serp_data(serp_data)
    
    # Format the prompt with all required variables
    prompt = prompt_template.format(
        keyword=keyword,
        serp_data=json.dumps(simplified_serp, indent=2),
        analysis_results=analysis_text,
        blog_content=blog_text,
        target_word_count=target_word_count,
        actual_word_count=actual_word_count,
        tone=tone
    )

    # Get LLM instance and generate critique
    try:
        # Determine the final model_id to use
        final_model_id = model_id  # Use function argument if provided
        if not final_model_id and llm_type == 'local':
            # Fallback to config only for local LLM if no specific model was passed
            final_model_id = config_manager.get_api_key('local_llm_model') or None

        llm = get_llm_instance(
            llm_type=llm_type,
            api_key=api_key,
            model_id=final_model_id,
            api_endpoint=api_endpoint
        )

        print(f"Critiquing blog content using {llm_type} (Model: {llm.model_id})...")
        critique_text = llm.generate(prompt, max_tokens=3000)
        
        # Create structured result
        critique_results = {
            'raw_critique': critique_text,
            'keyword': keyword,
            'llm_type': llm_type,
            'model_id': llm.model_id
        }
        
        # Try to extract scores and structured data if the LLM returned formatted content
        try:
            # Extract overall score if present (simple regex approach)
            import re
            overall_score_match = re.search(r'overall score:?\s*(\d+)', critique_text, re.IGNORECASE)
            if overall_score_match:
                critique_results['overall_score'] = int(overall_score_match.group(1))
                
            # Extract other metrics if present in a structured format
            # This is a basic approach - in a production system you might want to
            # have the LLM return JSON directly
        except Exception as parse_err:
            print(f"Warning: Could not parse structured data from critique: {parse_err}")
            # Continue with raw output if parsing fails
        
        return critique_results
        
    except Exception as e:
        print(f"Error during blog critique: {e}")
        raise Exception(f"Failed to critique blog content: {e}")


def simplify_serp_data(serp_data: dict) -> dict:
    """
    Extracts and simplifies the most relevant parts of the SERP data for critique.
    Reuses the function from llm_analyzer.py with slight modifications.
    
    Args:
        serp_data: The full SERP API response.
        
    Returns:
        A simplified dictionary with the most relevant parts.
    """
    simplified = {}
    
    # Extract organic results (titles, snippets, links)
    if 'organic_results' in serp_data:
        simplified['organic_results'] = []
        for result in serp_data['organic_results'][:10]:  # Limit to top 10
            simplified['organic_results'].append({
                'title': result.get('title', ''),
                'snippet': result.get('snippet', ''),
                'link': result.get('link', '')
            })
    
    # Extract "People Also Ask" questions
    if 'related_questions' in serp_data:
        simplified['related_questions'] = []
        for question in serp_data['related_questions']:
            simplified['related_questions'].append({
                'question': question.get('question', ''),
                'snippet': question.get('snippet', '')
            })
    
    # Extract related searches
    if 'related_searches' in serp_data:
        simplified['related_searches'] = []
        for search in serp_data['related_searches']:
            simplified['related_searches'].append(search.get('query', ''))
    
    return simplified


# Example usage (optional, for testing)
if __name__ == '__main__':
    # Test with sample data
    # This is similar to the test functions in other modules but adapted for critique
    pass
