import json
from .llm_interface import get_llm_instance
from . import config_manager
import re # Import re for placeholder check

def write_blog_post(analysis_results: dict, keyword: str, llm_type: str = None, api_key: str = None, model_id: str = None, api_endpoint: str = None, word_count: int = 1000, tone: str = "Professional", gatekeeper_feedback: str = "", is_rewrite: bool = False, previous_content: str = None) -> dict:
    """
    Generates or rewrites a blog post using an LLM based on SERP analysis results,
    optionally incorporating feedback from the Gatekeeper step (pre-writing or revision).

    Args:
        analysis_results: The analysis results from llm_analyzer.
        keyword: The original search keyword.
        llm_type: The type of LLM to use ('openai', 'gemini', 'anthropic', 'local', 'openrouter', 'groq').
        api_key: Optional API key (if not provided, will use config).
        model_id: Optional model ID (if not provided, will use default).
        api_endpoint: Optional API endpoint for local LLMs.
        word_count: Target word count for the blog post.
        tone: Writing tone to use (e.g., "Professional", "Conversational", "Educational", "Humorous").
        gatekeeper_feedback: Optional feedback/instructions from the Gatekeeper (pre-writing or revision).
        is_rewrite: Boolean flag indicating if this is a rewrite attempt based on Gatekeeper feedback.
        previous_content: The HTML content of the previous draft if is_rewrite is True.

    Returns:
        A dictionary containing the blog post content and metadata.

    Raises:
        Exception: If the blog writing fails.
    """
    # If LLM type not specified, get from config
    if not llm_type:
        llm_type = config_manager.get_pipeline_setting('writing_llm')
        if not llm_type:
            raise ValueError("No LLM type specified for blog writing and none found in config.")

    # If API key not provided, get from config based on LLM type
    if not api_key:
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        # API key validation will be done by the LLM implementation

    # Get the prompt template from config
    prompt_template = config_manager.get_prompt('writing_prompt')
    if not prompt_template:
        # Fallback to default prompt if not found in config
        prompt_template = prompts.get('writing_prompt')
        if not prompt_template:
            # Fallback to default prompt if not found in config
            print("Warning: Writing prompt not found in config, using default.")
            prompt_template = """
        **IMPORTANT:** Start your response *immediately* with the meta description, formatted exactly like this:
        META_DESCRIPTION: [Your concise meta description here, ~150-160 characters]

        Then, on a new line, provide the full blog post content.

        **Blog Post Task:**
        Write a comprehensive, SEO-optimized blog post about "{keyword}" using a {tone} tone. 
        
        Use the following analysis to guide your writing:
        {analysis_results}

        **IMPORTANT GUIDANCE FROM GATEKEEPER (Follow these instructions carefully):**
        {gatekeeper_feedback}
        ---
        
        {rewrite_context}

        The blog post should:
        1. Have an engaging title (using an `<h1>` tag) and introduction.
        2. Include at least 3 sections with appropriate headings (`<h2>` or `<h3>`).
        3. Naturally incorporate the short and long-tail keywords identified in the analysis.
        4. Address the People Also Ask questions from the analysis.
        5. Include a conclusion.
        6. Be approximately {word_count} words long.
        7. Be formatted in valid HTML with appropriate tags (`<h1>`, `<h2>`, `<p>`, `<ul>`, `<li>`, etc.).
        8. Maintain a {tone} tone throughout the content.
        
        Make the content informative, engaging, and valuable to readers interested in this topic. Remember to start with the META_DESCRIPTION line first.
        """

    # Format the prompt with the keyword, analysis results, and word count
    # If analysis_results is a dict with 'raw_analysis', use that, otherwise use the whole dict
    analysis_text = analysis_results.get('raw_analysis', json.dumps(analysis_results, indent=2))

    # Prepare feedback text - use provided feedback or a default message if empty
    feedback_text = gatekeeper_feedback.strip() if gatekeeper_feedback and gatekeeper_feedback.strip() else "No specific feedback provided."

    # Check if the template actually contains the placeholder before formatting
    if "{gatekeeper_feedback}" not in prompt_template:
         print("Warning: Writing prompt template does not contain '{gatekeeper_feedback}'. Gatekeeper feedback may not be used.")
         # Define rewrite_context_text before the format call
         rewrite_context_text = ""
         if is_rewrite and previous_content:
             rewrite_context_text = f"\n**Previous Draft (for context only, rewrite based on feedback):**\n---\n{previous_content}\n---"
         
         # If the placeholder is missing, we format without gatekeeper_feedback
         # Ensure rewrite_context placeholder exists or handle its absence
         if "{rewrite_context}" in prompt_template:
             prompt = prompt_template.format(
                 keyword=keyword,
                 analysis_results=analysis_text,
                 word_count=word_count,
                 tone=tone,
                 rewrite_context=rewrite_context_text # Add rewrite context if applicable
                 # gatekeeper_feedback is omitted
             )
         else: # Handle case where rewrite_context is also missing
              print("Warning: Writing prompt template also missing '{rewrite_context}'.")
              prompt = prompt_template.format(
                 keyword=keyword,
                 analysis_results=analysis_text,
                 word_count=word_count,
                 tone=tone
                 # Both gatekeeper_feedback and rewrite_context omitted
             )
    else:
         # Format with all placeholders if gatekeeper_feedback exists
         rewrite_context_text = ""
         if is_rewrite and previous_content:
             rewrite_context_text = f"\n**Previous Draft (for context only, rewrite based on feedback):**\n---\n{previous_content}\n---"
             
         prompt = prompt_template.format(
             keyword=keyword,
             analysis_results=analysis_text,
             word_count=word_count,
             tone=tone,
             gatekeeper_feedback=feedback_text, # Use prepared feedback text
             rewrite_context=rewrite_context_text # Add rewrite context if applicable
         )


    # Get LLM instance and generate blog post
    try:
        # Determine the final model_id to use:
        # Priority: 1. model_id passed to function, 2. config for local, 3. default in LLM class
        final_model_id = model_id # Use function argument if provided
        if not final_model_id and llm_type == 'local':
             # Fallback to config only for local LLM if no specific model was passed
             final_model_id = config_manager.get_api_key('local_llm_model') or None

        llm = get_llm_instance(
            llm_type=llm_type,
            api_key=api_key,
            model_id=final_model_id, # Pass the determined model_id
            api_endpoint=api_endpoint
        )

        action = "Rewriting" if is_rewrite else "Writing"
        print(f"{action} blog post using {llm_type} (Model: {llm.model_id})...") # Log the actual model used
        full_llm_response = llm.generate(prompt, max_tokens=4000)  # Larger token limit for blog posts

        # --- Parse LLM Response ---
        meta_description = ""
        blog_content_html = full_llm_response # Default to full response if parsing fails

        response_lines = full_llm_response.strip().split('\n', 1)
        if len(response_lines) > 0 and response_lines[0].strip().startswith("META_DESCRIPTION:"):
            meta_description = response_lines[0].strip().replace("META_DESCRIPTION:", "").strip()
            if len(response_lines) > 1:
                 blog_content_html = response_lines[1].strip() # The rest is the blog content
            else:
                 blog_content_html = "" # Only meta description was returned? Handle gracefully.
            print(f"Extracted Meta Description: {meta_description}")
        else:
            print("Warning: Could not find META_DESCRIPTION line at the start of the LLM response.")
            # Keep blog_content_html as the full response

        # Extract title from the HTML content
        title = extract_title(blog_content_html)

        blog_result = {
            'title': title,
            'content_html': blog_content_html,
            'meta_description': meta_description, # Add meta description
            'keyword': keyword,
            'llm_type': llm_type,
            'tone': tone,
            'target_word_count': word_count,
            'actual_word_count': len(blog_content_html.split())
        }
        
        return blog_result
        
    except Exception as e:
        print(f"Error during blog writing: {e}")
        raise Exception(f"Failed to write blog post: {e}")


def extract_title(content: str) -> str:
    """
    Extracts the title from the blog content.
    
    Args:
        content: The blog content.
        
    Returns:
        The extracted title or a default title.
    """
    import re
    # Try to extract title from HTML h1 tag first, as it's most reliable
    h1_match = re.search(r'<h1.*?>(.*?)</h1>', content, re.IGNORECASE | re.DOTALL)
    if h1_match:
        # Clean up potential extra whitespace/tags within the h1
        title_text = re.sub(r'<.*?>', '', h1_match.group(1)).strip()
        if title_text:
            return title_text

    # If no h1, try the first non-empty line (after stripping potential meta desc)
    lines = content.strip().split('\n')
    for line in lines:
        stripped_line = line.strip()
        # Skip potential meta description line if it wasn't parsed out earlier
        if stripped_line.startswith("META_DESCRIPTION:"):
            continue
        # Remove HTML tags and check if the line has content
        text_content = re.sub(r'<.*?>', '', stripped_line).strip()
        if text_content:
            return text_content # Return the first line with actual text content
    
    # Fallback to default title
    return "Blog Post"


# Example usage (optional, for testing)
if __name__ == '__main__':
    import os
    from dotenv import load_dotenv
    from .serp_fetcher import fetch_serp_data
    from .llm_analyzer import analyze_serp_data
    
    # Load API keys from .env file
    load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))
    
    # Test with a sample keyword
    test_keyword = "australian made shaving brushes"
    serp_api_key = os.getenv('SERPAPI_API_KEY')
    openai_api_key = os.getenv('OPENAI_API_KEY')
    
    if serp_api_key and openai_api_key:
        try:
            print(f"Fetching SERP data for '{test_keyword}'...")
            serp_data = fetch_serp_data(test_keyword, serp_api_key)
            
            print("Analyzing SERP data with OpenAI...")
            analysis = analyze_serp_data(
                serp_data=serp_data,
                keyword=test_keyword,
                llm_type='openai',
                api_key=openai_api_key
            )
            
            print("Writing blog post with OpenAI...")
            blog = write_blog_post(
                analysis_results=analysis,
                keyword=test_keyword,
                llm_type='openai',
                api_key=openai_api_key,
                word_count=500,  # Shorter for testing
                # model_id="gpt-4" # Example: Uncomment to test specific model
            )

            print("\n--- Blog Post ---")
            print(f"Title: {blog['title']}")
            print(f"Word Count: {blog['actual_word_count']}")
            print("\nContent Preview (first 500 chars):")
            print(blog['content_html'][:500] + "...")
            
        except Exception as e:
            print(f"Test failed: {e}")
    else:
        print("API keys not found in .env file. Skipping test.")
